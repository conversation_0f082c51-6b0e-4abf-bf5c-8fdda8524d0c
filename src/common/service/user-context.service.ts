import { Injectable } from '@nestjs/common';
import { AsyncLocalStorage } from 'async_hooks';

interface UserContext {
  userId: number;
  userName?: string;
  userEmail?: string;
  action?: string;
}

@Injectable()
export class UserContextService {
  private static asyncLocalStorage = new AsyncLocalStorage<UserContext>();

  /**
   * Set the current user context for the request
   */
  static setContext(context: UserContext): void {
    this.asyncLocalStorage.enterWith(context);
  }

  /**
   * Get the current user context
   */
  static getContext(): UserContext | undefined {
    return this.asyncLocalStorage.getStore();
  }

  /**
   * Get the current user ID
   */
  static getCurrentUserId(): number | undefined {
    return this.getContext()?.userId;
  }

  /**
   * Get the current user name
   */
  static getCurrentUserName(): string | undefined {
    return this.getContext()?.userName;
  }

  /**
   * Execute a function with a specific user context
   */
  static async runWithContext<T>(
    context: UserContext,
    fn: () => Promise<T>,
  ): Promise<T> {
    return new Promise((resolve, reject) => {
      this.asyncLocalStorage.run(context, async () => {
        try {
          const result = await fn();
          resolve(result);
        } catch (error) {
          reject(error);
        }
      });
    });
  }
}
