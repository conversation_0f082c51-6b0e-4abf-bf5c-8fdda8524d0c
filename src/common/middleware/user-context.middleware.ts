import { Injectable, NestMiddleware } from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { UserContextService } from '../service/user-context.service';

@Injectable()
export class UserContextMiddleware implements NestMiddleware {
  use(req: Request, res: Response, next: NextFunction): void {
    // Extract user info from the request (adjust based on your auth implementation)
    const user = (req as any).user; // Assuming you have user in request from auth guard

    if (user) {
      UserContextService.setContext({
        userId: user.id,
        userName: user.name || user.username,
        userEmail: user.email,
        action: `${req.method} ${req.path}`,
      });
    }

    next();
  }
}
