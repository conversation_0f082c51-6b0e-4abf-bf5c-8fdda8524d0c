/**
 * Parses comma-separated string of numbers into array of numbers
 * @param commaSeparatedIds - String like "1,2,3,4,5"
 * @returns Array of numbers [1, 2, 3, 4, 5]
 */
export function parseCommaSeparatedIds(commaSeparatedIds?: string): number[] {
  if (!commaSeparatedIds || commaSeparatedIds.trim() === '') {
    return [];
  }
  return commaSeparatedIds
    .split(',')
    .map((id) => parseInt(id.trim(), 10))
    .filter((id) => !isNaN(id) && id > 0);
}

/**
 * Parses comma-separated string into array of strings
 * @param commaSeparatedValues - String like "value1,value2,value3"
 * @returns Array of strings ["value1", "value2", "value3"]
 */
export function parseCommaSeparatedStrings(
  commaSeparatedValues?: string,
): string[] {
  if (!commaSeparatedValues || commaSeparatedValues.trim() === '') {
    return [];
  }
  return commaSeparatedValues
    .split(',')
    .map((value) => value.trim())
    .filter((value) => value.length > 0);
}
