import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository, Not } from 'typeorm';
import { CallLog } from '../entities/call-log.entity';
import { Lead } from '@modules/leads/entities/lead.entity';
import { CallStatus, CallType, Tab } from '../enums/call-log.enum';
import { User } from '@modules/users/entities/user.entity';
import { Phone } from '@modules/contacts/entities/phone.entity';
import { EventEmitter2 } from '@nestjs/event-emitter';
import {
  getOrderObject,
  mapCallStatusToCallAttendStatus,
} from '../utils/call-log.util';
import { CallLogFilterDto } from '../dto/call-log-filter.dto';
import { processCallLogs } from '../utils/format-utils';
import { buildQueryCondition } from '../utils/query-builder';
import {
  createEmptyPaginationResponse,
  createPaginationMeta,
} from 'src/common/utils/pagination-utils';
import { UserService } from '@modules/users/services/user.service';
import { PusherData } from '@modules/notifications/dto/pusher-data.dto';

/**
 * Service responsible for managing call logs including creation, updates, and assignments
 */
@Injectable()
export class CallLogService {
  private readonly logger = new Logger(CallLogService.name);

  constructor(
    @InjectRepository(CallLog)
    private callLogRepository: Repository<CallLog>,
    private readonly userService: UserService,
    private readonly eventEmitter: EventEmitter2,
  ) {}

  /**
   * Creates a new call log instance without saving it to the database
   */
  createCallLogInstance(
    phone: Phone,
    lead: Lead,
    user: User,
    call_type: CallType,
  ): CallLog {
    try {
      this.logger.log(
        `Creating call log instance for user ${user.id}, phone ${phone.id}, call type ${call_type}`,
      );
      const callLog = new CallLog();
      callLog.lead = lead;
      callLog.phone = phone;
      callLog.spoc = user;
      callLog.call_type = call_type;

      this.logger.debug(`Call log instance created successfully`);
      return callLog;
    } catch (error) {
      this.logger.error(
        `Failed to create call log instance: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Updates an existing call log or creates a new one if it doesn't exist
   */
  async updateCallLog(call: Partial<CallLog>) {
    this.logger.log(
      `Updating call log${call?.id ? ` with ID ${call.id}` : ' (new call)'}`,
    );
    try {
      if (call?.id) {
        return await this.updateExistingCallLog(call);
      } else {
        return await this.createNewCallLog(call);
      }
    } catch (error) {
      this.logger.error(
        `Failed to update/create call log: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Updates an existing call log
   */
  private async updateExistingCallLog(call: Partial<CallLog>) {
    this.logger.debug(`Finding existing call log with ID ${call.id}`);
    let callToUpdate = await this.callLogRepository.findOne({
      where: { id: call.id },
    });

    if (!callToUpdate) {
      this.logger.warn(`Call log with ID ${call.id} not found for update`);
      return this.callLogRepository.save(call);
    }

    this.logger.debug(`Merging call log data for ID ${call.id}`);
    callToUpdate = { ...callToUpdate, ...call };
    const result = await this.callLogRepository.save(callToUpdate);
    this.logger.log(`Call log with ID ${call.id} updated successfully`);
    return result;
  }

  /**
   * Creates a new call log
   */
  private async createNewCallLog(call: Partial<CallLog>) {
    this.logger.debug(`Creating new call log entry`);
    const result = await this.callLogRepository.save(call);
    this.logger.log(`New call log created with ID ${result.id}`);
    return result;
  }

  /**
   * Retrieves a specific call log by its ID including phone relation
   */
  findOneById(id: number) {
    this.logger.log(`Finding call log by ID: ${id}`);
    try {
      return this.callLogRepository.findOne({
        where: { id },
        relations: {
          phone: true,
          lead: true,
          spoc: true,
        },
      });
    } catch (error) {
      this.logger.error(
        `Error finding call log by ID ${id}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Associates a lead with all untracked calls from the same phone number
   */
  async assignleadToUntrackedCalls(lead: Lead, callLogId: number) {
    this.logger.log(
      `Assigning lead ID ${lead.id} to untracked calls for call ID ${callLogId}`,
    );
    try {
      const callLog = await this.findCallLog(callLogId);
      const allCalls = await this.findCallLogsWithPhoneId(callLog.phone.id);

      await this.assignLeadToCalls(lead, allCalls);

      return allCalls;
    } catch (error) {
      this.logger.error(
        `Failed to assign lead to untracked calls: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Find call with phone relation
   */
  private async findCallLog(callLogId: number): Promise<CallLog> {
    const callLog = await this.callLogRepository.findOne({
      where: { id: callLogId },
      relations: { phone: true },
    });

    if (!callLog) {
      this.logger.warn(
        `Call with ID ${callLogId} not found for lead assignment`,
      );
      throw new HttpException('Call not found', HttpStatus.NOT_FOUND);
    }

    return callLog;
  }

  /**
   * Find all calls for a given phone ID
   */
  private async findCallLogsWithPhoneId(phoneId: number): Promise<CallLog[]> {
    this.logger.debug(`Finding all calls from phone ID ${phoneId}`);
    const callLogs = await this.callLogRepository.find({
      where: { phone: { id: phoneId } },
    });

    this.logger.log(`Found ${callLogs.length} calls for phone ID ${phoneId}`);

    return callLogs;
  }

  /**
   * Assign lead to multiple call logs
   */
  private async assignLeadToCalls(lead: Lead, calls: CallLog[]): Promise<void> {
    for (const call of calls) {
      call.lead = lead;
      await this.callLogRepository.save(call);
      this.logger.debug(
        `Assigned lead ID ${lead.id} to call log ID ${call.id}`,
      );
    }

    this.logger.log(
      `Successfully assigned lead ID ${lead.id} to ${calls.length} calls`,
    );
  }

  /**
   * Gets user's most recent call log
   */
  async getUserRecentCallLog(user: User): Promise<CallLog | null> {
    this.logger.log(`Getting most recent call log for user ID ${user.id}`);
    try {
      const callLog = await this.callLogRepository.findOne({
        where: { spoc: { id: user.id } },
        order: { created_at: 'DESC' },
      });

      if (callLog) {
        this.logger.log(
          `Found recent call log ID ${callLog.id} for user ID ${user.id}`,
        );
      } else {
        this.logger.log(`No recent call logs found for user ID ${user.id}`);
      }

      return callLog;
    } catch (error) {
      this.logger.error(
        `Failed to get user's recent call log: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Updates call record when agent answers an inbound call
   */
  async updateAgentCallAttendance(callData: any): Promise<CallLog> {
    this.logger.log(
      `Processing agent call attendance update for call ID: ${callData.id}`,
    );
    return this.updateCallAttendanceBase(callData, true);
  }

  /**
   * Updates call record when customer answers an outbound call
   */
  async updateCustomerCallAttendance(callData: any): Promise<CallLog> {
    this.logger.log(
      `Processing customer call attendance update for call ID: ${callData.id}`,
    );
    return this.updateCallAttendanceBase(callData, false);
  }

  /**
   * Base method for updating call attendance with configurable relation loading
   */
  private async updateCallAttendanceBase(
    callData: any,
    isAgent?: boolean,
  ): Promise<CallLog> {
    const attendanceType = isAgent ? 'agent' : 'customer';
    this.logger.log(
      `Updating ${attendanceType} call attendance for call ID: ${callData.id}`,
    );

    try {
      // Get call and update attendance data
      const call = await this.getAndUpdateCallAttendance(callData, isAgent);

      // Emit event for real-time notification
      this.emitCallAttendanceEvent(call, callData);

      // Return call with relations based on requested detail level
      this.logger.log(
        `Successfully updated ${attendanceType} call attendance for call ID ${call.id}`,
      );
      return this.findOneWithDetailedRelations(call.id);
    } catch (error) {
      this.logger.error(
        `Error updating ${attendanceType} call attendance: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Get and update call attendance data
   */
  private async getAndUpdateCallAttendance(
    callData: any,
    isAgent: boolean,
  ): Promise<CallLog> {
    const call = await this.getCallByCallId(callData.id, callData.isMobile);

    if (!call) {
      throw new NotFoundException(`Call with ID ${callData.id} not found`);
    }

    // Update based on who answered (agent or customer)
    if (isAgent) {
      call.user_answer_stamp = callData.user_answer_stamp;
      call.user_ring_time = callData.ring_time;
    } else {
      call.customer_answer_stamp = callData.customer_answer_stamp;
      call.customer_ring_time = callData.ring_time;
    }

    call.call_attend_status = mapCallStatusToCallAttendStatus(
      callData.call_status,
    );

    return this.updateCallLog(call);
  }

  /**
   * Emit event for call attendance updates
   */
  private emitCallAttendanceEvent(call: CallLog, callData: any): void {
    if (call?.spoc?.uuid) {
      // Use different event types based on call flow
      const eventName = callData.customer_answer_stamp
        ? 'call.was_answered_by_customer'
        : 'call.was_answered_by_agent';

      this.logger.log(`Emitting event ${eventName} for call ID ${call.id}`);

      // Extract pusher data from call log
      const pusherData: PusherData = {
        callLogId: call?.id,
        callAttendStatus: call.call_attend_status,
        callType: call.call_type,
        candidateId: call.lead?.contact?.candidate_id || null,
        candidateName: call.lead?.contact?.full_name || 'Unknown',
        leadUuid: call.lead?.lead_uuid || '',
        leadId: call.lead?.id?.toString() || '',
        phoneId: call?.phone?.id || null,
        maskedPhoneNumber:
          call.phone?.masked_phone_number ||
          call.phone?.masked_phone_number ||
          '',
        spocUuid: call.spoc?.uuid || '',
      };

      this.logger.debug(
        `Pusher data prepared for call ID ${call.id}:`,
        JSON.stringify(pusherData),
      );

      this.eventEmitter.emit(eventName, pusherData);
    } else {
      this.logger.warn(
        `Cannot emit call attendance event: No SPOC UUID found for call ID ${call.id}`,
      );
    }
  }

  /**
   * Find call log with all necessary relations for detailed view
   */
  async findOneWithDetailedRelations(id: number): Promise<CallLog> {
    this.logger.log(`Finding call log with detailed relations for ID: ${id}`);
    try {
      const call = await this.callLogRepository.findOne({
        where: { id },
        relations: {
          phone: true,
          spoc: true,
          lead: {
            program_interests: true,
            contact: true,
          },
        },
      });

      if (call) {
        this.logger.debug(
          `Successfully retrieved detailed call log for ID: ${id}`,
        );
      } else {
        this.logger.warn(`No call log found with ID: ${id}`);
      }

      return call;
    } catch (error) {
      this.logger.error(
        `Error finding call log with relations for ID ${id}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Get call log by call ID
   * Loads call with detailed relationships for associated entities
   */
  async getCallByCallId(
    callId: number | string,
    isMobile: boolean,
  ): Promise<CallLog> {
    this.logger.log(`Getting call by ID: ${callId}, isMobile: ${isMobile}`);
    try {
      // Find call based on ID type
      let call: CallLog;

      if (isMobile) {
        call = await this.findCallByLocalId(Number(callId));
      } else {
        call = await this.findCallBySystemId(Number(callId));
      }

      if (!call) {
        this.logger.warn(
          `Call with ID ${callId} (isMobile: ${isMobile}) not found`,
        );
        throw new NotFoundException(`Call with ID ${callId} not found`);
      }

      this.logger.log(
        `Successfully retrieved call with ID ${callId}, database ID: ${call.id}`,
      );
      return call;
    } catch (error) {
      this.logger.error(
        `Error getting call by ID ${callId}: ${error.message}`,
        error.stack,
      );
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new HttpException(
        `Error retrieving call: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Find call by local database ID
   */
  private async findCallByLocalId(id: number): Promise<CallLog> {
    return this.callLogRepository.findOne({
      where: { id },
      relations: this.getDetailedCallRelations(),
    });
  }

  /**
   * Find call by system call ID
   */
  private async findCallBySystemId(callId: number): Promise<CallLog> {
    return this.callLogRepository.findOne({
      where: { call_id: callId },
      relations: this.getDetailedCallRelations(),
    });
  }

  /**
   * Get standard detailed relations for call queries
   */
  private getDetailedCallRelations() {
    return {
      lead: {
        contact: {
          emails: true,
          phones: true,
          primary_email: true,
          primary_phone: true,
        },
        program_interests: true,
      },
      phone: true,
      spoc: true,
      approved_by: true,
    };
  }

  /**
   * Remove a lead from missed calls tracking
   */
  async removeFromMissedCalls(leadId: number): Promise<void> {
    if (!leadId) {
      this.logger.debug(
        `No lead ID provided for missed calls removal, skipping`,
      );
      return;
    }

    this.logger.log(`Removing lead ID ${leadId} from missed calls tracking`);
    try {
      const calls = await this.findMissedCallsByLeadId(leadId);

      if (!calls || calls.length === 0) {
        this.logger.log(`No missed calls found for lead ID ${leadId}`);
        return;
      }

      await this.updateMissedCallsToWasMissed(calls);
    } catch (error) {
      this.logger.error(
        `Failed to remove missed calls for lead ID ${leadId}: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Failed to remove missed calls',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Find missed calls for a specific lead
   */
  private async findMissedCallsByLeadId(leadId: number): Promise<CallLog[]> {
    return this.callLogRepository.find({
      where: {
        lead: { id: leadId },
        call_status: CallStatus.MISSED,
        call_type: CallType.INCOMING,
      },
    });
  }

  /**
   * Update missed calls to WAS_MISSED status
   */
  private async updateMissedCallsToWasMissed(calls: CallLog[]): Promise<void> {
    this.logger.log(`Updating ${calls.length} missed calls to WAS_MISSED`);

    const updatedCalls: CallLog[] = calls.map((call) => {
      call.call_status = CallStatus.WAS_MISSED;
      return call;
    });

    await this.callLogRepository.save(updatedCalls);
    this.logger.log(`Successfully updated missed calls status`);
  }

  /**
   * Gets filtered call logs based on tab selection and other filters
   */
  async getFilteredCallLogs(
    filterDto: CallLogFilterDto,
    user: User,
  ): Promise<any> {
    try {
      this.logger.log(
        `Getting filtered call logs for tab: ${filterDto.tab_id}`,
      );

      // Convert page and size to numbers
      const pageNum = Number(filterDto.page);
      const sizeNum = Number(filterDto.size);

      // Get user IDs for filtering using the UserService
      const userIds = await this.getUserIdsForFilter(filterDto.users, user);
      this.logger.debug(`Filtering calls for users: ${userIds.join(', ')}`);

      // Build query conditions
      const condition = buildQueryCondition(filterDto, userIds);

      // Check if this tab requires grouping
      const shouldGroup =
        filterDto.tab_id === Tab.missed_calls ||
        filterDto.tab_id === Tab.ivr_missed_calls;

      if (shouldGroup) {
        // Handle grouped pagination with optimized query
        return await this.getOptimizedGroupedCallLogs(
          condition,
          filterDto,
          pageNum,
          sizeNum,
          userIds,
        );
      } else {
        // Handle regular pagination
        return await this.getRegularCallLogs(
          condition,
          filterDto,
          pageNum,
          sizeNum,
        );
      }
    } catch (error) {
      this.logger.error(
        `Error getting filtered call logs: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        `Failed to get filtered call logs: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
  /**
   * Handle optimized pagination for grouped call logs using database-level grouping
   */
  private async getOptimizedGroupedCallLogs(
    condition: any,
    filterDto: CallLogFilterDto,
    pageNum: number,
    sizeNum: number,
    userIds: number[],
  ): Promise<any> {
    try {
      // Step 1: Get paginated unique phone IDs with their latest call info
      const skip = (pageNum - 1) * sizeNum;

      // Get unique phone IDs with pagination using subquery
      // Note: We need to use the actual foreign key column name
      const phoneIdsSubquery = this.callLogRepository
        .createQueryBuilder('call_log')
        .leftJoin('call_log.phone', 'phone')
        .select('DISTINCT phone.id', 'phone_id')
        .addSelect('MAX(call_log.created_at)', 'latest_created_at')
        .where(condition)
        .groupBy('phone.id')
        .orderBy('latest_created_at', 'DESC')
        .offset(skip)
        .limit(sizeNum);

      const paginatedPhoneIds = await phoneIdsSubquery.getRawMany();

      if (paginatedPhoneIds.length === 0) {
        return createEmptyPaginationResponse(pageNum, sizeNum);
      }

      const phoneIds = paginatedPhoneIds.map((item) => item.phone_id);

      // Step 2: Get call logs for these specific phone IDs with all relations
      const callsForPaginatedPhones = await this.callLogRepository.find({
        where: {
          ...condition,
          phone: { id: In(phoneIds) },
        },
        relations: {
          phone: true,
          lead: {
            program_interests: {
              program: true,
              lead_level: true,
            },
            contact: true,
          },
          spoc: { manager: true },
        },
        order: { created_at: 'DESC' },
      });

      // Step 3: Group the calls and maintain order
      const { groupedCalls } = processCallLogs(
        callsForPaginatedPhones,
        filterDto.tab_id,
      );

      // Sort grouped calls by the latest call timestamp to maintain consistent ordering
      const sortedGroupedCalls = groupedCalls.sort(
        (a, b) =>
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime(),
      );

      // Step 4: Get total count of unique phone IDs for pagination metadata
      const totalUniquePhones = await this.getGroupedCountForTab(
        filterDto.tab_id,
        filterDto,
        userIds,
      );

      // Step 5: Get total raw calls count for additional metadata
      const totalRawCalls = await this.callLogRepository.count({
        where: condition,
      });

      return {
        data: sortedGroupedCalls,
        meta: {
          ...createPaginationMeta(totalUniquePhones, pageNum, sizeNum),
          total_calls: totalRawCalls,
          grouped_count: sortedGroupedCalls.length,
        },
      };
    } catch (error) {
      this.logger.error(
        `Error in optimized grouped call logs: ${error.message}`,
        error.stack,
      );
    }
  }

  /**
   * Handle pagination for regular call logs (non-grouped)
   */
  private async getRegularCallLogs(
    condition: any,
    filterDto: CallLogFilterDto,
    pageNum: number,
    sizeNum: number,
  ): Promise<any> {
    const skip = (pageNum - 1) * sizeNum;

    // Get total count for pagination
    const totalCallsWithoutGrouping = await this.callLogRepository.count({
      where: condition,
    });

    if (totalCallsWithoutGrouping === 0) {
      return createEmptyPaginationResponse(pageNum, sizeNum);
    }

    // Get paginated call logs
    const calls = await this.fetchFilteredCallLogs(
      condition,
      filterDto,
      skip,
      sizeNum,
    );

    // Process call data for response (no grouping)
    const { groupedCalls } = processCallLogs(calls, filterDto.tab_id);

    return {
      data: groupedCalls,
      meta: {
        ...createPaginationMeta(totalCallsWithoutGrouping, pageNum, sizeNum),
        total_calls: totalCallsWithoutGrouping,
      },
    };
  }

  /**
   * Get user IDs to include in filter (current user or specified users)
   * Now using the UserService for subordinate-related functionality
   */
  async getUserIdsForFilter(userIds: number[], user: User): Promise<number[]> {
    // If specific users are provided in filter, use those
    if (userIds && userIds?.length > 0) {
      this.logger.debug(
        `Using explicitly provided user IDs: ${userIds.join(', ')}`,
      );
      return userIds;
    }

    try {
      // Check if the user has any subordinates
      const hasSubordinates = await this.userService.hasSubordinates(user.id);

      if (hasSubordinates) {
        // Get all subordinate IDs including the current user using UserService
        const allUserIds = await this.userService.getAllSubordinateIdsWithUser(
          user.id,
        );
        this.logger.log(
          `Found ${allUserIds.length} users (including subordinates) for filtering`,
        );
        return allUserIds;
      }
    } catch (error) {
      this.logger.warn(
        `Error fetching subordinates: ${error.message}. Using only current user ID.`,
      );
    }

    // Default: just use the current user ID
    return [user.id];
  }

  /**
   * Fetch filtered call logs with relations and sorting
   */
  private async fetchFilteredCallLogs(
    condition: any,
    filterDto: CallLogFilterDto,
    skip: number,
    take: number,
  ): Promise<CallLog[]> {
    return this.callLogRepository.find({
      where: condition,
      relations: {
        phone: true,
        lead: {
          program_interests: {
            program: true,
            lead_level: true,
          },
          contact: true,
        },
        spoc: { manager: true },
      },
      order:
        filterDto.sort_field && filterDto.sort_field !== 'count'
          ? getOrderObject(filterDto.sort_field, filterDto.sort_order)
          : { created_at: 'DESC' },
      skip,
      take,
    });
  }

  /**
   * Get counts for all tabs based on filter criteria
   */
  async getTabCounts(
    filterDto: Partial<CallLogFilterDto>,
    user: User,
  ): Promise<any> {
    try {
      // Get user IDs for filtering
      const userIds = await this.getUserIdsForFilter(filterDto.users, user);

      // Get counts for tabs that need grouping separately
      const [missedCallsCount, ivrMissedCallsCount] = await Promise.all([
        this.getGroupedCountForTab(Tab.missed_calls, filterDto, userIds),
        this.getGroupedCountForTab(Tab.ivr_missed_calls, filterDto, userIds),
      ]);

      // Get counts for regular tabs
      const [
        untrackedCallsCount,
        toUpdateCallsCount,
        dialledCallsCount,
        connectedCallsCount,
        ivrTodayCount,
        mtdIvrCallsCount,
        allCallLogsCount,
      ] = await Promise.all([
        this.getCountForTab(Tab.untracked_calls, filterDto, userIds),
        this.getCountForTab(Tab.to_update_calls, filterDto, userIds),
        this.getCountForTab(Tab.dialled_calls, filterDto, userIds),
        this.getCountForTab(Tab.connected_calls, filterDto, userIds),
        this.getCountForTab(Tab.ivr_today, filterDto, userIds),
        this.getCountForTab(Tab.mtd_ivr_calls, filterDto, userIds),
        this.getCountForTab(Tab.Call_Logs, filterDto, userIds),
      ]);

      return {
        data: {
          [Tab.missed_calls]: missedCallsCount,
          [Tab.ivr_missed_calls]: ivrMissedCallsCount,
          [Tab.untracked_calls]: untrackedCallsCount,
          [Tab.to_update_calls]: toUpdateCallsCount,
          [Tab.dialled_calls]: dialledCallsCount,
          [Tab.connected_calls]: connectedCallsCount,
          [Tab.ivr_today]: ivrTodayCount,
          [Tab.mtd_ivr_calls]: mtdIvrCallsCount,
          [Tab.Call_Logs]: allCallLogsCount,
        },
      };
    } catch (error) {
      this.logger.error(
        `Error getting tab counts: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        `Failed to get tab counts: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Get count for a specific tab
   */
  private async getCountForTab(
    tab: Tab,
    filterDto: Partial<CallLogFilterDto>,
    userIds: number[],
  ): Promise<number> {
    const tabFilterDto = { ...filterDto, tab_id: tab } as CallLogFilterDto;
    const condition = buildQueryCondition(tabFilterDto, userIds);
    return this.callLogRepository.count({ where: condition });
  }

  /**
   * Get count for tabs that require grouping by phone number
   */
  private async getGroupedCountForTab(
    tab: Tab,
    filterDto: Partial<CallLogFilterDto>,
    userIds: number[],
  ): Promise<number> {
    try {
      const tabFilterDto = { ...filterDto, tab_id: tab } as CallLogFilterDto;
      const condition = buildQueryCondition(tabFilterDto, userIds);

      // Use COUNT with DISTINCT for better performance
      const result = await this.callLogRepository
        .createQueryBuilder('call_log')
        .leftJoin('call_log.phone', 'phone')
        .select('COUNT(DISTINCT phone.id)', 'count')
        .where(condition)
        .getRawOne();

      return parseInt(result.count) || 0;
    } catch (error) {
      this.logger.error(
        `Error getting grouped count for tab ${tab}: ${error.message}`,
        error.stack,
      );
      return 0;
    }
  }

  async getNonDisposedCallLogs(userId: number) {
    this.logger.log(`Getting non-disposed call logs for user ID: ${userId}`);
    try {
      // Find all call logs where disposition is null
      const callLogs = await this.callLogRepository.find({
        where: {
          spoc: { id: userId },
          is_disposed: false,
          call_status: CallStatus.ATTENDED,
        },
        relations: {
          lead: {
            contact: true,
          },
        },
      });

      // Transform the data to include required fields and exclude the lead object
      const transformedCallLogs = callLogs.map((callLog) => {
        // Extract the needed information
        const candidateId = callLog.lead?.contact?.candidate_id || null;

        // Create a new object without the lead property
        const { lead, ...callLogWithoutLead } = callLog;

        // Return the call log with the additional fields but without the lead object
        return {
          ...callLogWithoutLead,
          candidate_id: candidateId,
          lead_uuid: lead?.lead_uuid || null,
          lead_id: lead?.id || null,
        };
      });

      return {
        data: transformedCallLogs,
        meta: {
          total: transformedCallLogs.length,
          page: 1,
          size: transformedCallLogs.length,
          totalPages: 1,
          hasNextPage: false,
          hasPreviousPage: false,
        },
      };
    } catch (error) {
      this.logger.error(
        `Error getting non-disposed call logs for user ID ${userId}: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        `Failed to get non-disposed call logs: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async updateDisposition({
    dispositionId,
    callLogId,
  }: {
    dispositionId: number;
    callLogId: number;
  }) {
    try {
      this.logger.log(
        `Updating disposition for call log ID ${callLogId} with disposition ID ${dispositionId}`,
      );

      // Find the call log to update
      const callLog = await this.callLogRepository.findOne({
        where: { id: callLogId },
      });

      if (!callLog) {
        this.logger.warn(`Call log with ID ${callLogId} not found`);
        throw new NotFoundException(`Call log with ID ${callLogId} not found`);
      }

      // Update the disposition history ID
      callLog.disposition_history_id = dispositionId;
      callLog.is_disposed = true;

      // Save the updated call log
      const updatedCallLog = await this.callLogRepository.save(callLog);

      this.logger.log(
        `Successfully updated disposition for call log ID ${callLogId}`,
      );
      return updatedCallLog;
    } catch (error) {
      this.logger.error(
        `Error updating disposition for call log ID ${callLogId}: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        `Failed to update disposition: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Mark all other non-disposed calls for the same lead as disposed
   * @param leadId - ID of the lead
   * @param currentCallLogId - ID of the current call log that is being disposed (to exclude from update)
   */
  async markOtherCallsAsDisposed(
    leadId: number,
    currentCallLogId: number,
  ): Promise<void> {
    this.logger.log(
      `Marking other non-disposed calls as disposed for lead ID: ${leadId}, excluding call log ID: ${currentCallLogId}`,
    );

    try {
      // Find all non-disposed calls for the same lead, excluding the current call log
      const nonDisposedCalls = await this.callLogRepository.find({
        where: {
          lead: { id: leadId },
          is_disposed: false,
          id: Not(currentCallLogId), // Exclude the current call log
        },
      });

      if (nonDisposedCalls.length === 0) {
        this.logger.log(
          `No other non-disposed calls found for lead ID: ${leadId}`,
        );
        return;
      }

      this.logger.log(
        `Found ${nonDisposedCalls.length} other non-disposed calls for lead ID: ${leadId}`,
      );

      // Mark all these calls as disposed
      const updatedCalls = nonDisposedCalls.map((call) => {
        call.is_disposed = true;
        return call;
      });

      // Save all updated calls
      await this.callLogRepository.save(updatedCalls);

      this.logger.log(
        `Successfully marked ${updatedCalls.length} calls as disposed for lead ID: ${leadId}`,
      );
    } catch (error) {
      this.logger.error(
        `Error marking other calls as disposed for lead ID ${leadId}: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Failed to mark other calls as disposed',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Validate call log and its associations
   */
  async validateCallLog(
    callLogId: number,
    leadId: number,
    spocUserId: number,
  ): Promise<void> {
    const callLog = await this.findOneById(callLogId);

    if (!callLog) {
      throw new BadRequestException(`Call log with ID ${callLogId} not found`);
    }

    // Check if call log belongs to the same lead
    if (callLog.lead?.id !== leadId) {
      throw new BadRequestException(
        `Call log ${callLogId} does not belong to lead ${leadId}`,
      );
    }

    // Check if call log belongs to the same user
    if (callLog.spoc?.id !== spocUserId) {
      throw new BadRequestException(
        `Call log ${callLogId} does not belong to user ${spocUserId}`,
      );
    }
  }

  /**
   * Gets the last connected call dates for multiple leads in a batch
   * @param leadIds Array of lead IDs
   * @returns Map of lead ID to last connected call date
   */
  async getLastConnectedCallDatesForLeads(
    leadIds: number[],
  ): Promise<Map<number, Date | null>> {
    const resultMap = new Map<number, Date | null>();

    if (leadIds.length === 0) {
      this.logger.debug('No lead IDs provided for batch call date lookup');
      return resultMap;
    }

    try {
      this.logger.log(
        `Getting last connected call dates for ${leadIds.length} leads`,
      );

      // Use explicit join to access lead.id properly
      const query = this.callLogRepository
        .createQueryBuilder('call_log')
        .leftJoin('call_log.lead', 'lead')
        .select([
          'lead.id as lead_id',
          'MAX(call_log.created_at) as last_connected_call',
        ])
        .where('lead.id IN (:...leadIds)', { leadIds })
        .andWhere('call_log.call_status = :status', {
          status: CallStatus.ATTENDED,
        })
        .groupBy('lead.id');

      const results = await query.getRawMany();

      // Initialize all leads with null
      leadIds.forEach((id) => resultMap.set(id, null));

      // Update with actual dates where calls exist
      results.forEach((result) => {
        const leadId = parseInt(result.lead_id);
        const lastCallDate = result.last_connected_call
          ? new Date(result.last_connected_call)
          : null;
        resultMap.set(leadId, lastCallDate);
      });

      this.logger.debug(
        `Found connected call dates for ${results.length} out of ${leadIds.length} leads`,
      );
      return resultMap;
    } catch (error) {
      this.logger.error(
        `Error fetching last connected call dates for leads: ${error.message}`,
        error.stack,
      );
      // Return map with all null values in case of error
      leadIds.forEach((id) => resultMap.set(id, null));
      return resultMap;
    }
  }
}
