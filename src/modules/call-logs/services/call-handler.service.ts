import {
  Injectable,
  Logger,
  HttpException,
  HttpStatus,
  NotFoundException,
  forwardRef,
  Inject,
} from '@nestjs/common';
import { CallLogService } from './call-log.service';
import { PhoneResolverService } from './phone-resolver.service';
import { LeadResolverService } from './lead-resolver.service';
import { CallService } from './call.service';
import { Phone } from '@modules/contacts/entities/phone.entity';
import { User } from '@modules/users/entities/user.entity';
import { CallLog } from '../entities/call-log.entity';
import { CallType, CallStatus, CallAttendStatus } from '../enums/call-log.enum';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { FileStorageService } from '@modules/storage/services/file-storage.service';
import { transformCallLogToDto } from '../utils/call-log.util';
import { CallLogResponseDto } from '../dto/call-log-response.dto';
import { LeadHistoryService } from '@modules/lead-histories/services/lead-history.service';
import {
  ActionType,
  EngagementSubActionType,
} from '@modules/lead-histories/enums/lead-history.enum';
import { PusherData } from '@modules/notifications/dto/pusher-data.dto';
import { UserService } from '@modules/users/services/user.service';
import { CallEvents } from '@modules/notifications/enums/call-events.enum';
import { CallProcessingService } from './call-processing.service';

@Injectable()
export class CallHandlerService {
  private readonly logger = new Logger(CallHandlerService.name);

  constructor(
    private readonly callLogService: CallLogService,
    private readonly phoneResolverService: PhoneResolverService,
    private readonly leadResolverService: LeadResolverService,
    @Inject(forwardRef(() => CallService))
    private readonly callService: CallService,
    private userService: UserService,
    private readonly eventEmitter: EventEmitter2,
    private readonly fileStorageService: FileStorageService,
    private readonly leadHistoryService: LeadHistoryService,
    private readonly callProcessingService: CallProcessingService,
  ) {}

  /**
   * Routes call to appropriate handler based on user's calling mode
   */
  async processCall(phone: Phone, user: User, callLog: CallLog): Promise<void> {
    return await this.callService.processCall(phone, user, callLog);
  }

  /**
   * Enhanced call processing with intelligent lead assignment
   */
  async callRecievedOnServer(data: any): Promise<CallLogResponseDto> {
    this.logger.log(
      `Call received on server with data: customer phone ${data?.customer_phone}, employee ID: ${data?.user?.employee_id}`,
    );

    try {
      // Find the user
      const user = await this.findUser(data?.user?.employee_id);

      // Handle virtual number call early
      if (this.isVirtualNumberCall(data, user)) {
        return await this.handleVirtualNumberCall(user);
      }

      // Process phone and assign lead using the dedicated service
      const callLogEntity =
        await this.callProcessingService.processPhoneAndAssignLead(data, user);

      // Transform to DTO and emit events
      const callLogDto = transformCallLogToDto(callLogEntity);
      this.emitCallReceivedEvent(callLogEntity);

      return callLogDto;
    } catch (error) {
      this.logger.error(
        `Error in callRecievedOnServer: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        error.message || 'Failed to process received call',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Process call termination and update associated data
   */
  async processCallTermination(data: any) {
    this.logger.log(
      `Processing call termination for call ID: ${data.id}, isMobile: ${data.isMobile}`,
    );

    try {
      // Get call by ID
      const call = await this.getCallForTermination(data);

      // Update call with termination data
      const updatedCall = await this.callService.updateCallWithTerminationData(
        call,
        data,
      );

      // Handle post-call operations
      await this.leadResolverService.performPostCallLeadOperations(updatedCall);

      // Create call history record
      this.createCallHistory(updatedCall);

      // Emit termination event
      this.emitCallTerminatedEvent(updatedCall);

      this.logger.log(
        `Call termination processing completed successfully for call ID: ${updatedCall.id}`,
      );
      return updatedCall;
    } catch (error) {
      this.logger.error(
        `Error processing call termination: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Process call recording update
   */
  async processCallRecordingUpdate(call: CallLog, file: Express.Multer.File) {
    try {
      Logger.debug(`Processing call recording update for call: ${call.id}`);

      const uploadResult = await this.fileStorageService.uploadPublicFile(
        file,
        'call_recordings_mobile_local',
      );

      call.call_recording_url = uploadResult.url;
      return await this.callLogService.updateCallLog(call);
    } catch (error) {
      Logger.error(
        `Error processing call recording update: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Create call history record
   */
  async createCallHistory(call: CallLog) {
    this.logger.log(`Creating call history record for call ID: ${call.id}`);

    try {
      const leadId = call?.lead?.id;
      const contactId = call?.phone?.contact_id;

      this.logger.debug(
        `Associated lead ID: ${leadId}, contact ID: ${contactId}`,
      );

      const typeOfCall = this.determineCallTypeForHistory(call);
      this.logger.debug(`Determined call type: ${typeOfCall}`);

      const callDetails = {
        callType: typeOfCall,
        startTime: call.start_stamp,
        date: new Date(Date.now()).toISOString(),
        duration: call.duration,
        recordingUrl: call.call_recording_url,
        callId: call.id,
        isIvr: call.is_IVR,
      };

      const payload = {
        action: ActionType.ENGAGEMENT,
        subAction: EngagementSubActionType.CALLS,
        leadId,
        contactId,
        performedByUser: call?.spoc,
        performedBy: call?.spoc?.id,
        details: callDetails,
      };

      this.logger.debug(
        `Creating lead history with payload: ${JSON.stringify(payload)}`,
      );
      this.leadHistoryService.createLeadHistory(payload);
      this.logger.log(
        `Call history record created successfully for call ID: ${call.id}`,
      );
    } catch (err) {
      this.logger.error(
        `Error creating call history record: ${err.message}`,
        err.stack,
      );
    }
  }

  // Private helper methods

  private async findUser(employeeId: string): Promise<User> {
    this.logger.debug(`Looking up user with employee ID: ${employeeId}`);
    const user = await this.userService.findByEmpId(employeeId);
    this.logger.debug(`User lookup result: ${user ? 'found' : 'not found'}`);

    if (!user) {
      this.logger.warn(`User with employee ID ${employeeId} not found`);
      throw new NotFoundException(
        `User with employee ID ${employeeId} not found`,
      );
    }

    this.logger.debug(
      `Found user with ID ${user.id} for employee ID ${employeeId}`,
    );
    return user;
  }

  private isVirtualNumberCall(data: any, user: User): boolean {
    return (
      data.customer_country_code + data.customer_phone === user.virtual_number
    );
  }

  private async handleVirtualNumberCall(
    user: User,
  ): Promise<CallLogResponseDto> {
    this.logger.log(`Virtual number call detected for user ${user.id}`);
    const recentCallEntity =
      await this.callLogService.getUserRecentCallLog(user);
    return transformCallLogToDto(recentCallEntity);
  }

  private async getCallForTermination(data: any): Promise<CallLog> {
    this.logger.debug(`Retrieving call record for ID: ${data.id}`);
    const call = await this.callLogService.getCallByCallId(
      data.id,
      data.isMobile,
    );

    if (!call) {
      this.logger.warn(
        `Call with ID ${data.id} not found for termination processing`,
      );
      throw new HttpException('Call not found', HttpStatus.NOT_FOUND);
    }

    this.logger.debug(`Retrieved call record with database ID: ${call.id}`);
    return call;
  }

  private emitCallReceivedEvent(callLogEntity: CallLog): void {
    const pusherData = this.createPusherData(callLogEntity);

    this.logger.log(
      `Emitting call.was_received event for call ID: ${callLogEntity.id}, spocUuid: ${pusherData.spocUuid}`,
    );
    this.eventEmitter.emit(CallEvents.CALL_WAS_RECEIVED, pusherData);
  }

  private emitCallTerminatedEvent(callLog: CallLog): void {
    const pusherData = this.createPusherData(callLog);

    this.logger.log(
      `Emitting call.was_terminated event for call ID: ${callLog.id}, spocUuid: ${pusherData.spocUuid}`,
    );
    this.eventEmitter.emit(CallEvents.CALL_WAS_TERMINATED, pusherData);
  }

  private createPusherData(callLog: CallLog): PusherData {
    return {
      callAttendStatus: callLog.call_attend_status,
      callType: callLog.call_type,
      candidateId: callLog.lead?.contact?.candidate_id || null,
      candidateName: callLog.lead?.contact?.full_name || 'Unknown',
      leadUuid: callLog.lead?.lead_uuid || '',
      leadId: callLog.lead?.id?.toString() || '',
      maskedPhoneNumber: callLog.phone?.masked_phone_number || '',
      spocUuid: callLog.spoc?.uuid || '',
      callLogId: callLog.id,
      phoneId: callLog.phone?.id || null,
    };
  }

  private determineCallTypeForHistory(call: CallLog): string {
    return call?.call_attend_status ===
      CallAttendStatus.NOT_ANSWERED_BY_CUSTOMER ||
      (call?.call_type === CallType.INCOMING &&
        call?.call_attend_status === CallAttendStatus.ANSWERED_BY_USER)
      ? CallStatus.NOT_CONNECTED
      : call?.call_status === CallStatus.MISSED
        ? CallStatus.MISSED
        : call?.call_type === CallType.INCOMING
          ? CallType.INCOMING
          : CallType.OUTGOING;
  }
}
