import {
  forwardRef,
  HttpException,
  HttpStatus,
  Inject,
  Injectable,
  Logger,
} from '@nestjs/common';
import { InjectRedis } from '@nestjs-modules/ioredis';
import { Redis } from 'ioredis';
import { EventEmitter2 } from '@nestjs/event-emitter';
import axios from 'axios';

// Service imports
import { CallLogService } from '../services/call-log.service';
import { PhoneResolverService } from '../services/phone-resolver.service';
import { LeadResolverService } from '../services/lead-resolver.service';
import { CallHandlerService } from '../services/call-handler.service';

// Entity imports
import { User } from '@modules/users/entities/user.entity';
import { CallLog } from '../entities/call-log.entity';
import { Phone } from '@modules/contacts/entities/phone.entity';

// DTO imports
import { InitiateCallDto } from '../dto/create-call.dto';
import { CallHangupDto } from '../dto/call-hangup.dto';
import { UpdateCallRecordingDto } from '../dto/update-call-recording.dto';

// Enum imports
import { CallType, CallStatus, CallAttendStatus } from '../enums/call-log.enum';
import { CallingMode } from '@modules/users/enums/calling-mode.enum';
import { PusherData } from '@modules/notifications/dto/pusher-data.dto';

// Config imports
import { AppConfig } from 'src/config/app.config';

// Utility imports
import { mapCallStatusToCallAttendStatus } from '../utils/call-log.util';
import { CallProcessingService } from './call-processing.service';

/**
 * Service handling telephony integration operations
 * Orchestrates call lifecycle: initiation, registration, termination, recordings
 */
@Injectable()
export class CallService {
  private readonly logger = new Logger(CallService.name);

  constructor(
    // Repositories
    // Services with circular dependencies
    @Inject(forwardRef(() => CallLogService))
    private readonly calllogService: CallLogService,

    // Helper services
    private readonly phoneResolver: PhoneResolverService,
    private readonly leadResolver: LeadResolverService,
    private readonly callHandler: CallHandlerService,
    private readonly eventEmitter: EventEmitter2,
    private readonly callProcessingService: CallProcessingService,

    // Cache
    @InjectRedis() private readonly redis: Redis,

    // Config
    private readonly appConfig: AppConfig,
  ) {}

  /**
   * Initiates an outgoing call from agent to customer
   * Resolves contact info and creates call log before initiating call
   */
  async initiateOutgoingCall(
    dto: InitiateCallDto,
    user: User,
    clientId: number,
  ) {
    try {
      this.logger.debug(`Initiating outgoing call to ${dto.phone_number}`);

      // Resolve contact information and prepare call
      const phone = await this.phoneResolver.resolvePhone(dto);
      const lead = await this.leadResolver.resolveLead(phone, clientId);

      // Create call log entry
      const callLog = await this.calllogService.createCallLogInstance(
        phone,
        lead,
        user,
        CallType.OUTGOING,
      );

      // Initiate the actual call
      await this.processCall(phone, user, callLog);

      return {
        statusCode: HttpStatus.OK,
        message: 'Call initiated successfully',
        data: callLog,
      };
    } catch (error) {
      this.logger.error(
        `Call initiation failed: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        error.message || 'Failed to initiate call',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Registers an incoming call from telephony system
   * Normalizes phone data and creates appropriate call records
   */
  async registerIncomingCall(data: any) {
    try {
      // Delegate to call handler service for processing
      return await this.callHandler.callRecievedOnServer(data);
    } catch (error) {
      this.logger.error(
        `Failed to register incoming call: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        error.message || 'Failed to register incoming call',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Handles call termination notifications from telephony system
   * Updates call records and triggers post-call workflows
   */
  async handleCallTermination(dto: CallHangupDto) {
    try {
      this.logger.debug(`Processing call termination for call: ${dto.id}`);

      // Format call data for termination handling
      const callData = {
        id: dto.id,
        hangup_cause: dto.hangup_cause,
        call_attend_status: dto.call_attend_status,
        customer_ring_time: dto.customer_ring_time,
        user_ring_time: dto.user_ring_time,
        end_stamp: dto.end_stamp || new Date(),
        recording_url: dto.recording_url,
        duration: dto.duration,
        isMobile: true,
      };

      // Delegate call termination processing to handler service
      const call = await this.callHandler.processCallTermination(callData);

      // Extract pusher data from call log
      const pusherData: PusherData = {
        callAttendStatus: call.call_attend_status,
        callType: call.call_type,
        candidateId: call.lead?.contact?.candidate_id || null,
        candidateName: call.lead?.contact?.full_name || 'Unknown',
        leadUuid: call.lead?.lead_uuid || '',
        leadId: call.lead?.id?.toString() || '',
        maskedPhoneNumber: call.phone?.masked_phone_number || '',
        spocUuid: call.spoc?.uuid || '',
        callLogId: call.id,
        phoneId: call.phone?.id || null,
      };

      // Emit event with the entity (not the DTO)
      this.logger.log(
        `Emitting call.was_terminated event for call ID: ${pusherData.spocUuid},pusherData: ${JSON.stringify(pusherData)}`,
      );

      // Emit event for real-time notifications and post-call processing
      this.eventEmitter.emit('call.was_terminated', pusherData);

      return call;
    } catch (error) {
      this.logger.error(
        `Failed to process call termination: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        error.message || 'Failed to process call termination',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Updates call record with uploaded recording file
   * Validates call and delegates to handler for storage operations
   */
  async handleCallRecordingUpdate(
    dto: UpdateCallRecordingDto,
    file: Express.Multer.File,
  ) {
    try {
      this.logger.debug(`Handling recording update for call: ${dto.call_id}`);

      // Validate call exists
      const call = await this.calllogService.getCallByCallId(dto.call_id, true);
      if (!call) {
        throw new HttpException('Call not found', HttpStatus.NOT_FOUND);
      }

      // Delegate to handler service for file upload and record update
      const updatedCall = await this.callHandler.processCallRecordingUpdate(
        call,
        file,
      );

      // Emit event for notifications and integrations
      this.eventEmitter.emit('call.recording.updated', updatedCall);

      return updatedCall;
    } catch (error) {
      this.logger.error(
        `Failed to update call recording: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        error.message || 'Failed to update call recording',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Create call log with new phone
   */
  async createCallLogWithNewPhone(
    phone: Phone,
    data: any,
    user: User,
  ): Promise<CallLog> {
    this.logger.debug(`Creating call log with new phone ID: ${phone.id}`);

    const call = new CallLog();
    call.is_IVR = data?.is_ivr === true;
    call.phone = phone;
    call.spoc = user;
    call.call_id = data?.id;
    call.start_stamp = data.start_stamp || new Date();
    call.user_answer_stamp = data.user_answer_stamp;
    call.call_type = data?.callType ?? CallType.INCOMING;
    call.call_attend_status = mapCallStatusToCallAttendStatus(data.call_status);
    call.call_status = CallStatus.MISSED;

    const savedCall = await this.calllogService.updateCallLog(call);
    this.logger.log(
      `Successfully created call log with ID: ${savedCall.id} for new phone`,
    );

    return savedCall;
  }

  /**
   * Create call log with existing phone
   */
  async createCallLogWithExistingPhone(
    phone: Phone,
    data: any,
    user: User,
  ): Promise<CallLog> {
    this.logger.log(`Creating call log with existing phone ID: ${phone.id}`);

    const call = new CallLog();
    call.is_IVR = data?.is_ivr === true;
    call.phone = phone;
    call.spoc = user;
    call.call_id = data?.id;
    call.start_stamp = data.start_stamp;
    call.user_answer_stamp = data.user_answer_stamp;
    call.call_type = data?.callType ?? CallType.INCOMING;
    call.call_status = CallStatus.MISSED;
    call.call_attend_status = mapCallStatusToCallAttendStatus(data.call_status);

    const savedCall = await this.calllogService.updateCallLog(call);
    this.logger.log(
      `Successfully created call log with ID: ${savedCall.id} for existing phone`,
    );

    return savedCall;
  }

  /**
   * Routes call to appropriate handler based on user's calling mode
   */
  async processCall(phone: Phone, user: User, callLog: CallLog): Promise<void> {
    this.logger.log(
      `Processing call for user ID ${user.id}, phone ID ${phone.id}, call mode: ${user.calling_mode}`,
    );

    try {
      if (user.calling_mode === CallingMode.LOCAL) {
        this.logger.log(`Handling local call for user ${user.id}`);
        await this.handleLocalCall(phone, user, callLog);
      } else {
        this.logger.log(`Handling telephony call for user ${user.id}`);
        await this.handleTelephonyCall(phone, user, callLog);
      }
      this.logger.log(
        `Call processing completed successfully for user ${user.id}`,
      );
    } catch (error) {
      this.logger.error(
        `Process call failed for user ${user.id}: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Call processing failed',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Handles local mode calling through device
   */
  private async handleLocalCall(
    phone: Phone,
    user: User,
    callLog: CallLog,
  ): Promise<void> {
    this.logger.log(
      `Handling local call for user ID: ${user.id}, phone ID: ${phone.id}`,
    );

    try {
      const customData = this.prepareLocalCallData(phone, callLog);
      this.logger.debug(
        `Prepared local call data: ${JSON.stringify(customData)}`,
      );
      // TODO: Implement notification sending
      this.logger.log(
        `Local call notification would be sent here (not implemented)`,
      );
    } catch (error) {
      this.logger.error(
        `Local call failed for user ${user.id}: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Local call failed',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Handles telephony service calling through external API
   */
  private async handleTelephonyCall(
    phone: Phone,
    user: User,
    callLog: CallLog,
  ): Promise<void> {
    this.logger.log(
      `Handling telephony call for user ID: ${user.id}, phone ID: ${phone.id}`,
    );

    try {
      this.logger.debug(
        `Making telephony API call for phone: ${phone.phone_number}`,
      );
      const response = await this.makeTeleponyApiCall(phone, user);
      this.logger.debug(
        `Telephony API call successful, received call ID: ${response.data.result.id}`,
      );

      callLog.call_id = response.data.result.id;
      await this.calllogService.updateCallLog(callLog);
      this.logger.log(
        `Telephony call processed successfully with call ID: ${callLog.call_id}`,
      );
    } catch (error) {
      this.logger.error(
        `Telephony call failed for user ${user.id}: ${error.message}`,
        error.stack,
      );
      throw new HttpException(
        'Telephony call failed',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  /**
   * Makes API call to external telephony service
   */
  private async makeTeleponyApiCall(phone: Phone, user: User) {
    this.logger.log(
      `Making telephony API call for user ID: ${user.id}, phone: ${phone.phone_number}`,
    );

    try {
      const { url, apiKey } = this.appConfig.telephony;

      this.logger.debug(`Telephony API URL: ${url}, API Key: ${apiKey}`);
      const headers = {
        accept: 'application/json',
        'content-type': 'application/json',
        'X-api-key': apiKey,
      };
      const body = {
        emp_id: user.employee_id,
        phone: phone.phone_number,
        country_code: phone.country_code,
      };

      this.logger.debug(
        `Calling telephony API at ${url} for employee ID: ${user.employee_id} and body ${JSON.stringify(body)}`,
      );
      const response = await axios.post(url, body, { headers });
      this.logger.debug(`Telephony API call successful`);
      return response;
    } catch (error) {
      this.logger.error(
        `Telephony API call failed: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Prepares data payload for local call
   */
  private prepareLocalCallData(phone: Phone, callLog: CallLog) {
    this.logger.debug(
      `Preparing local call data for phone: ${phone.phone_number}`,
    );
    return {
      action: 'trigger-call',
      phone: phone.country_code + phone.phone_number,
      name: callLog?.lead?.contact?.first_name ?? '',
      date_time: new Date().toISOString(),
    };
  }

  /**
   * Updates call record with termination data
   */
  async updateCallWithTerminationData(
    call: CallLog,
    data: any,
  ): Promise<CallLog> {
    this.logger.log(`Updating call ID ${call.id} with termination data`);

    try {
      // Update call timestamps and metadata
      this.logger.debug(
        `Updating timestamps and metadata for call ID: ${call.id}`,
      );
      call.end_stamp = data.end_stamp ?? call.end_stamp;
      call.call_recording_url = data.recording_url ?? call.call_recording_url;
      call.start_stamp = data.start_stamp ?? call.start_stamp;

      // Update call attendance details
      this.logger.debug(`Updating attendance details for call ID: ${call.id}`);
      call.customer_answer_stamp =
        data.customer_answer_stamp ?? call.customer_answer_stamp;
      call.user_answer_stamp = data.user_answer_stamp ?? call.user_answer_stamp;
      call.customer_ring_time =
        data.customer_ring_time ?? data.ring_time ?? call.customer_ring_time;
      call.user_ring_time =
        data.user_ring_time ?? data.ring_time ?? call.user_ring_time;
      call.hangup_cause = data.hangup_cause ?? call.hangup_cause;

      // Calculate call duration
      this.logger.debug(`Calculating call duration for call ID: ${call.id}`);
      this.calculateCallDuration(call, data);

      // Determine call attendance status based on data and existing values
      this.logger.debug(
        `Updating call attendance status for call ID: ${call.id}`,
      );
      this.updateCallAttendanceStatus(call, data);

      // Determine call status based on attendance status and call type
      this.logger.debug(`Updating call status for call ID: ${call.id}`);
      call.call_status =
        mapCallStatusToCallAttendStatus(data.call_status) ===
        CallAttendStatus.SUCCESS
          ? CallStatus.ATTENDED
          : CallStatus.MISSED;

      // Save and return updated call
      const updatedCall = await this.calllogService.updateCallLog(call);
      this.logger.log(
        `Call termination data update completed for call ID: ${updatedCall.id}`,
      );

      return updatedCall;
    } catch (error) {
      this.logger.error(
        `Error updating call with termination data: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Calculates call duration based on timestamps
   */
  private calculateCallDuration(call: CallLog, data: any): void {
    this.logger.debug(`Calculating call duration for call ID: ${call.id}`);

    try {
      // If duration is provided in data, use it directly
      if (data.duration !== undefined && data.duration !== null) {
        call.duration = data.duration;
        this.logger.debug(`Using provided duration: ${data.duration} seconds`);
        return;
      }

      // For mobile calls, calculate based on appropriate timestamps
      if (data.isMobile === true) {
        this.logger.debug(
          `Calculating duration for mobile call, type: ${call.call_type}`,
        );

        let answerStamp: Date | null = null;
        if (call.call_type === CallType.OUTGOING) {
          answerStamp = call.customer_answer_stamp;
          this.logger.debug(
            `Using customer answer stamp for outgoing call: ${answerStamp}`,
          );
        } else if (call.call_type === CallType.INCOMING) {
          answerStamp = call.user_answer_stamp;
          this.logger.debug(
            `Using user answer stamp for incoming call: ${answerStamp}`,
          );
        }

        // Calculate duration if we have both answer and end timestamps
        if (answerStamp && call.end_stamp) {
          const endEpoch = Math.floor(call.end_stamp.getTime() / 1000);
          const answerEpoch = Math.floor(answerStamp.getTime() / 1000);
          call.duration = endEpoch - answerEpoch;
          this.logger.debug(`Calculated duration: ${call.duration} seconds`);
        } else {
          call.duration = 0;
          this.logger.debug(
            `Missing timestamps for duration calculation, setting to 0`,
          );
        }
      } else {
        call.duration = data.duration ?? null;
        this.logger.debug(
          `Using provided duration for non-mobile call: ${call.duration}`,
        );
      }

      this.logger.debug(
        `Call duration calculated: ${call.duration} seconds for call ID: ${call.id}`,
      );
    } catch (error) {
      this.logger.error(`Failed to calculate call duration: ${error.message}`);
      call.duration = 0;
      this.logger.debug(`Setting duration to 0 due to calculation error`);
    }
  }

  /**
   * Updates call attendance status based on data and call flow
   */
  private updateCallAttendanceStatus(call: CallLog, data: any): void {
    this.logger.debug(
      `Updating call attendance status for call ID: ${call.id}`,
    );

    // If explicit attendance status is provided, use it
    if (data.call_status) {
      call.call_attend_status = mapCallStatusToCallAttendStatus(
        data.call_status,
      );
      this.logger.debug(
        `Using provided attendance status: ${call.call_attend_status}`,
      );
      return;
    }

    // Determine attendance status based on call type and timestamps
    if (call.call_type === CallType.OUTGOING) {
      if (call.customer_answer_stamp) {
        call.call_attend_status = CallAttendStatus.ANSWERED_BY_CUSTOMER;
        this.logger.debug(`Outgoing call was answered by customer`);
      } else {
        call.call_attend_status = CallAttendStatus.NOT_ANSWERED_BY_CUSTOMER;
        this.logger.debug(`Outgoing call was not answered by customer`);
      }
    } else if (call.call_type === CallType.INCOMING) {
      if (call.user_answer_stamp) {
        call.call_attend_status = CallAttendStatus.ANSWERED_BY_USER;
        this.logger.debug(`Incoming call was answered by user`);
      } else {
        call.call_attend_status = CallAttendStatus.NOT_ANSWERED_BY_USER;
        this.logger.debug(`Incoming call was not answered by user`);
      }
    }

    // Handle special hangup causes
    if (
      call.hangup_cause === 'ORIGINATOR_CANCEL' &&
      call.call_type === CallType.OUTGOING
    ) {
      call.call_attend_status = CallAttendStatus.HANGUP_BY_CUSTOMER;
      this.logger.debug(`Call was hung up by customer (ORIGINATOR_CANCEL)`);
    }

    if (
      ['NORMAL_TEMPORARY_FAILURE', 'DESTINATION_OUT_OF_ORDER'].includes(
        call.hangup_cause,
      )
    ) {
      call.call_attend_status = CallAttendStatus.FAILED;
      this.logger.debug(
        `Call failed due to technical reason: ${call.hangup_cause}`,
      );
    }

    this.logger.debug(
      `Final call attendance status: ${call.call_attend_status}`,
    );
  }
}
