import { Entity, Column } from 'typeorm';
import { ClientAwareEntity } from 'src/common/entities/client-aware.entity';

@Entity('message_templates')
export class MessageTemplate extends ClientAwareEntity {
  @Column({ type: 'varchar', length: 100, nullable: false })
  event_name: string;

  @Column({ type: 'text', nullable: true })
  definition: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ type: 'varchar', length: 200, nullable: true })
  email_template_name: string;

  @Column({ type: 'varchar', length: 200, nullable: true })
  whatsapp_template_name: string;

  @Column({ type: 'varchar', length: 255, nullable: true })
  email_subject: string;

  @Column({ type: 'text', nullable: true })
  whatsapp_message_template: string;

  @Column({ type: 'text', nullable: true })
  email_message_template: string;

  @Column({ type: 'boolean', default: false })
  is_whatsapp: boolean;

  @Column({ type: 'boolean', default: false })
  is_email: boolean;
}
