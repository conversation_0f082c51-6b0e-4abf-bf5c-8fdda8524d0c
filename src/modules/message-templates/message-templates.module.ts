import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MessageTemplatesService } from './message-templates.service';
import { MessageTemplatesController } from './message-templates.controller';
import { MessageTemplate } from './entities/message-templates.entity';
import { LeadModule } from '@modules/leads/lead.module';
import { CallbackModule } from '@modules/callbacks/callback.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([MessageTemplate]),
    LeadModule,
    CallbackModule,
  ],
  controllers: [MessageTemplatesController],
  providers: [MessageTemplatesService],
  exports: [MessageTemplatesService],
})
export class MessageTemplatesModule {}
