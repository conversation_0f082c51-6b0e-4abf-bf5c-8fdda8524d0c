import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { MessageTemplate } from './entities/message-templates.entity';
import { CreateMessageTemplateDto } from './dto/create-message-template.dto';
import { UpdateMessageTemplateDto } from './dto/update-message-template.dto';
import { SendMessageDto } from './dto/send-whatsapp.dto';
import { SendEmailDto } from './dto/send-email.dto';
import { LeadService } from '@modules/leads/services/lead.service';
import { Lead } from '@modules/leads/entities/lead.entity';
import { EventNames } from './enums/message-templates.enums';
import { OutboundCallbackService } from '@modules/callbacks/outbounds/services/outbound.callback.service';

@Injectable()
export class MessageTemplatesService {
  constructor(
    @InjectRepository(MessageTemplate)
    private readonly messageTemplateRepository: Repository<MessageTemplate>,
    private readonly leadService: LeadService,
    private readonly outboundCallbackService: OutboundCallbackService,
  ) {}

  async create(
    dto: CreateMessageTemplateDto,
    clientId: number,
    userId: number,
  ): Promise<MessageTemplate> {
    const messageTemplate = this.messageTemplateRepository.create({
      ...dto,
      client_id: clientId,
      created_by: userId,
      updated_by: userId,
    });

    return this.messageTemplateRepository.save(messageTemplate);
  }

  async findAll(clientId: number): Promise<MessageTemplate[]> {
    return this.messageTemplateRepository.find({
      where: { client_id: clientId, is_active: true },
      order: { event_name: 'ASC' },
    });
  }

  async findOne(id: number, clientId: number): Promise<MessageTemplate> {
    const messageTemplate = await this.messageTemplateRepository.findOne({
      where: { id, client_id: clientId, is_active: true },
    });

    if (!messageTemplate) {
      throw new NotFoundException('Message template not found');
    }

    return messageTemplate;
  }

  async update(
    dto: UpdateMessageTemplateDto,
    clientId: number,
    userId: number,
  ): Promise<MessageTemplate> {
    const messageTemplate = await this.findOne(dto.id, clientId);

    Object.assign(messageTemplate, {
      event_name: dto.event_name ?? messageTemplate.event_name,
      definition: dto.definition ?? messageTemplate.definition,
      description: dto.description ?? messageTemplate.description,
      email_template_name:
        dto.email_template_name ?? messageTemplate.email_template_name,
      whatsapp_template_name:
        dto.whatsapp_template_name ?? messageTemplate.whatsapp_template_name,
      email_subject: dto.email_subject ?? messageTemplate.email_subject,
      whatsapp_message_template:
        dto.whatsapp_message_template ??
        messageTemplate.whatsapp_message_template,
      email_message_template:
        dto.email_message_template ?? messageTemplate.email_message_template,
      is_whatsapp: dto.is_whatsapp ?? messageTemplate.is_whatsapp,
      is_email: dto.is_email ?? messageTemplate.is_email,
      updated_by: userId,
    });

    return this.messageTemplateRepository.save(messageTemplate);
  }

  async remove(id: number, clientId: number, userId: number): Promise<void> {
    const messageTemplate = await this.findOne(id, clientId);

    messageTemplate.is_active = false;
    messageTemplate.updated_by = userId;

    await this.messageTemplateRepository.save(messageTemplate);
  }

  async getWhatsAppTemplates(clientId: number): Promise<MessageTemplate[]> {
    return this.messageTemplateRepository.find({
      where: {
        client_id: clientId,
        is_active: true,
        is_whatsapp: true,
      },
      order: { id: 'ASC' },
    });
  }

  async getEmailTemplates(clientId: number): Promise<MessageTemplate[]> {
    return this.messageTemplateRepository.find({
      where: {
        client_id: clientId,
        is_active: true,
        is_email: true,
      },
      order: { id: 'ASC' },
    });
  }

  private replaceVariables(
    template: string,
    variables: Record<string, any> = {},
  ): string {
    let result = template;
    Object.keys(variables).forEach((key) => {
      const regex = new RegExp(`{{${key}}}`, 'g');
      result = result.replace(regex, variables[key] || '');
    });
    return result;
  }

  async sendWhatsApp(
    dto: SendMessageDto,
    clientId: number,
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Find WhatsApp template by event name
      const template = await this.messageTemplateRepository.findOne({
        where: {
          client_id: clientId,
          event_name: dto.event_name,
          is_whatsapp: true,
          is_active: true,
        },
      });

      if (!template) {
        throw new NotFoundException(
          `WhatsApp template not found for event: ${dto.event_name}`,
        );
      }

      const lead = await this.leadService.findOne(dto.lead_id);

      if (!lead) {
        throw new NotFoundException(`Lead not found: ${dto.lead_id}`);
      }

      if (!lead.contact.primary_phone) {
        throw new NotFoundException(
          `Primary phone not found for lead: ${dto.lead_id}`,
        );
      }
      const payload = this.getWhatsappmessagePayload(dto.event_name, lead);
      this.outboundCallbackService.sendMessageEvent(payload);
      return {
        success: true,
        message: 'WhatsApp message sent successfully',
      };
    } catch (error) {
      console.log(error);
      return {
        success: false,
        message: error.message,
      };
    }
  }

  getWhatsappmessagePayload(event_name: EventNames, lead: Lead) {
    // Common payload structure for all events
    const basePayload = {
      lead_id: lead.id,
      first_name: lead.contact.first_name,
      last_name: lead.contact.last_name,
      full_name: lead.contact.full_name,
      uuid: lead.lead_uuid,
      email: lead.contact.primary_email?.email,
      phone: lead.contact.primary_phone?.phone_number,
      country_code: lead.contact.primary_phone?.country_code,
      event_name: event_name,
      send_whatsapp: true,
      send_email: false,
    };

    switch (event_name) {
      case EventNames.DID_NOT_VISIT_AND_NOT_INTERESTED_DOCTORS:
      case EventNames.DID_NOT_VISIT_AND_POSTPONED:
      case EventNames.CALLED_POSITIVE:
      case EventNames.CALLED_COMING:
      case EventNames.READY_TO_ENROLL:
      case EventNames.VISITED_BUT_POSTPONED:
      case EventNames.VISITED_BUT_NOT_INTERESTED:
      case EventNames.VISITED_POSITIVE:
      case EventNames.VISITED_READY_TO_ENROLL:
      case EventNames.APPLICATION_SUBMITTED:
      case EventNames.APPLICATION_SELECTED:
      case EventNames.APPLICATION_REJECTED_DROPPED:
      case EventNames.PAYMENT_2500_PAID:
        return basePayload;
      default:
        return null;
    }
  }

  async sendEmail(
    dto: SendMessageDto,
    clientId: number,
  ): Promise<{ success: boolean; message: string }> {
    try {
      // Find Email template by event name
      const template = await this.messageTemplateRepository.findOne({
        where: {
          client_id: clientId,
          event_name: dto.event_name,
          is_email: true,
          is_active: true,
        },
      });

      if (!template) {
        throw new NotFoundException(
          `Email template not found for event: ${dto.event_name}`,
        );
      }

      const lead = await this.leadService.findOne(dto.lead_id);

      if (!lead) {
        throw new NotFoundException(`Lead not found: ${dto.lead_id}`);
      }

      if (!lead.contact.primary_phone) {
        throw new NotFoundException(
          `Primary phone not found for lead: ${dto.lead_id}`,
        );
      }

      const payload = this.getEmailPayload(dto.event_name, lead);
      this.outboundCallbackService.sendMessageEvent(payload);

      return {
        success: true,
        message: 'Email sent successfully',
      };
    } catch (error) {
      console.log(error);
      return {
        success: false,
        message: error.message,
      };
    }
  }
  getEmailPayload(event_name: EventNames, lead: Lead) {
    // Common payload structure for all events
    const basePayload = {
      lead_id: lead.id,
      first_name: lead.contact.first_name,
      last_name: lead.contact.last_name,
      full_name: lead.contact.full_name,
      uuid: lead.lead_uuid,
      email: lead.contact.primary_email?.email,
      phone: lead.contact.primary_phone?.phone_number,
      country_code: lead.contact.primary_phone?.country_code,
      event_name: event_name,
      send_whatsapp: false,
      send_email: true,
    };

    switch (event_name) {
      case EventNames.DID_NOT_VISIT_AND_NOT_INTERESTED_DOCTORS:
      case EventNames.DID_NOT_VISIT_AND_POSTPONED:
      case EventNames.CALLED_POSITIVE:
      case EventNames.CALLED_COMING:
      case EventNames.READY_TO_ENROLL:
      case EventNames.VISITED_BUT_POSTPONED:
      case EventNames.VISITED_BUT_NOT_INTERESTED:
      case EventNames.VISITED_POSITIVE:
      case EventNames.VISITED_READY_TO_ENROLL:
      case EventNames.APPLICATION_SUBMITTED:
      case EventNames.APPLICATION_SELECTED:
      case EventNames.APPLICATION_REJECTED_DROPPED:
      case EventNames.PAYMENT_2500_PAID:
        return basePayload;
      default:
        return null;
    }
  }
}
