import { Module } from '@nestjs/common';
import { WebinarReportsController } from './controllers/webinar-report.controller';
import { WebinarReportsService } from './services/webinar-report.service';
import { WebinarModule } from '@modules/webinars/webinar.module';
import { CallLogModule } from '@modules/call-logs/call-log.module';
import { UserModule } from '@modules/users/user.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WebinarRegistration } from '@modules/webinars/entities/webinar-registration.entity';

@Module({
  imports: [
    WebinarModule,
    CallLogModule,
    UserModule,
    TypeOrmModule.forFeature([WebinarRegistration]),
  ],
  controllers: [WebinarReportsController],
  providers: [WebinarReportsService],
})
export class ReportModule {}
