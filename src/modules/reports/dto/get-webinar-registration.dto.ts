import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  IsString,
  IsDateString,
  Min,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
} from 'class-validator';
import { Type, Transform } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  AttendStatus,
  WebinarStatus,
} from '@modules/webinars/entities/webinar-registration.entity';
import { SessionStatus } from '../enums/webinar-report.enum';

export class GetWebinarRegistrationsDto {
  @ApiProperty({
    description: 'ID of the webinar to get registrations for',
    example: 123,
    type: 'number',
  })
  @IsNumber()
  @Type(() => Number)
  webinar_id: number;

  @ApiPropertyOptional({
    description: 'Filter by webinar attendance status',
    example: WebinarStatus.attended,
    enum: WebinarStatus,
    enumName: 'WebinarStatus',
  })
  @IsOptional()
  @IsEnum(WebinarStatus)
  webinar_status?: WebinarStatus;

  @ApiPropertyOptional({
    description: 'Page number for pagination',
    example: 1,
    default: 1,
    minimum: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Type(() => Number)
  @Transform(({ value }) => value || 1)
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Number of items per page',
    example: 10,
    default: 10,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @IsNumber()
  @Min(1)
  @Max(100)
  @Type(() => Number)
  @Transform(({ value }) => value || 10)
  page_size?: number = 10;

  @ApiPropertyOptional({
    description:
      'Search pattern for candidate ID, email, name, or phone number',
    example: '<EMAIL>',
    type: 'string',
  })
  @IsOptional()
  @IsString()
  pattern?: string;

  @ApiPropertyOptional({
    description:
      'Comma-separated SPOC (Sales Point of Contact) user IDs to filter by',
    example: '1,2,3,4,5',
    type: 'string',
  })
  @IsOptional()
  @IsString()
  spoc_ids?: string;

  @ApiPropertyOptional({
    description: 'Comma-separated attendance statuses to filter by',
    example: 'pending,confirmed,not_attending',
    type: 'string',
    enum: AttendStatus,
  })
  @IsOptional()
  @IsString()
  attend_status?: string;

  @ApiPropertyOptional({
    description: 'Comma-separated webinar session statuses to filter by',
    example: 'not_joined,in_session,left_session',
    type: 'string',
    enum: SessionStatus,
  })
  @IsOptional()
  @IsString()
  session_status?: string;

  @ApiPropertyOptional({
    description: 'Start date for registration date filter (ISO 8601 format)',
    example: '2025-01-01T00:00:00.000Z',
    type: 'string',
    format: 'date-time',
  })
  @IsOptional()
  @IsDateString()
  registration_start_date?: string;

  @ApiPropertyOptional({
    description: 'End date for registration date filter (ISO 8601 format)',
    example: '2025-12-31T23:59:59.999Z',
    type: 'string',
    format: 'date-time',
  })
  @IsOptional()
  @IsDateString()
  registration_end_date?: string;

  @ApiPropertyOptional({
    description: 'Start date for last call date filter (ISO 8601 format)',
    example: '2025-01-01T00:00:00.000Z',
    type: 'string',
    format: 'date-time',
  })
  @IsOptional()
  @IsDateString()
  last_call_start_date?: string;

  @ApiPropertyOptional({
    description: 'End date for last call date filter (ISO 8601 format)',
    example: '2025-12-31T23:59:59.999Z',
    type: 'string',
    format: 'date-time',
  })
  @IsOptional()
  @IsDateString()
  last_call_end_date?: string;
}
