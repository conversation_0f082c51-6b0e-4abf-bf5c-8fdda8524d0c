import { AttendStatus } from '@modules/webinars/entities/webinar-registration.entity';
import { GetWebinarRegistrationsDto } from './get-webinar-registration.dto';
import { SessionStatus } from '../enums/webinar-report.enum';

export interface ExtendedWebinarRegistrationsDto
  extends GetWebinarRegistrationsDto {
  parsedSpocIds?: number[];
  parsedAttendStatuses?: AttendStatus[];
  parsedSessionStatuses?: SessionStatus[];
}
