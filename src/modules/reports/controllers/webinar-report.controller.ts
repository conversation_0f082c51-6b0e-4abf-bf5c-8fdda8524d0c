import { Controller, Get, Query, Req } from '@nestjs/common';
import { WebinarReportsService } from '../services/webinar-report.service';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { AuthenticatedRequest } from '@modules/auth/interfaces/request.interface';
import { GetWebinarRegistrationsDto } from '../dto/get-webinar-registration.dto';
import { PaginatedResponse } from 'src/common/interfaces/pagination.interface';

@ApiTags('Webinar Reports')
@Controller('webinar-reports')
export class WebinarReportsController {
  constructor(private readonly webinarReportsService: WebinarReportsService) {}

  @Get('registrations')
  @ApiOperation({
    summary: 'Get webinar registrations with filters and pagination',
  })
  @ApiResponse({
    status: 200,
    description: 'Successfully retrieved paginated webinar registrations',
  })
  async getWebinarRegistrations(
    @Query() dto: GetWebinarRegistrationsDto,
    @Req() req: AuthenticatedRequest,
  ): Promise<PaginatedResponse<any>> {
    const clientId = req.user.currentClientId;
    return this.webinarReportsService.getWebinarRegistrations(
      dto,
      req.user,
      clientId,
    );
  }
}
