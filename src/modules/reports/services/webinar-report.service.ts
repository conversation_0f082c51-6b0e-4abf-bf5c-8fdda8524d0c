import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { WebinarService } from '../../webinars/services/webinar.service';
import {
  AttendStatus,
  WebinarRegistration,
} from '../../webinars/entities/webinar-registration.entity';
import { CallLogService } from '../../call-logs/services/call-log.service';
import { WebinarRegistrationUtil } from '../utils/webinar-registration.util';
import { User } from '@modules/users/entities/user.entity';
import { PaginatedResponse } from 'src/common/interfaces/pagination.interface';
import { UserService } from '@modules/users/services/user.service';
import { GetWebinarRegistrationsDto } from '../dto/get-webinar-registration.dto';
import {
  parseCommaSeparatedIds,
  parseCommaSeparatedStrings,
} from 'src/common/utils/common-funtions.util';
import { SessionStatus } from '../enums/webinar-report.enum';

@Injectable()
export class WebinarReportsService {
  private readonly logger = new Logger(WebinarReportsService.name);

  constructor(
    private readonly webinarService: WebinarService,
    private readonly callLogService: CallLogService,
    private readonly userService: UserService,
    @InjectRepository(WebinarRegistration)
    private readonly webinarRegistrationRepository: Repository<WebinarRegistration>,
  ) {}

  async getWebinarRegistrations(
    dto: GetWebinarRegistrationsDto,
    user: User,
    clientId: number,
  ): Promise<PaginatedResponse<any>> {
    this.logger.log(
      `Fetching webinar registrations for webinar ID: ${dto.webinar_id}`,
    );

    try {
      // Check if user has admin access
      const hasAdminAccess = await this.userService.isUserAdminOrSuperAdmin(
        user.id,
        clientId,
      );
      const spocIds = parseCommaSeparatedIds(dto.spoc_ids);
      const attendStatusStrings = parseCommaSeparatedStrings(dto.attend_status);
      const sessionStatusStrings = parseCommaSeparatedStrings(
        dto.session_status,
      );
      // Convert string arrays to enum arrays
      const attendStatuses = attendStatusStrings
        .filter((status) =>
          Object.values(AttendStatus).includes(status as AttendStatus),
        )
        .map((status) => status as AttendStatus);

      const sessionStatuses = sessionStatusStrings
        .filter((status) =>
          Object.values(SessionStatus).includes(status as SessionStatus),
        )
        .map((status) => status as SessionStatus);

      let userIds: number[] = [];
      // Only get user IDs for filtering if user doesn't have admin access OR specific spoc_ids are provided
      if (!hasAdminAccess || spocIds?.length > 0) {
        userIds = await this.callLogService.getUserIdsForFilter(spocIds, user);
        this.logger.log(`User IDs for filtering: ${userIds.join(', ')}`);
      } else {
        this.logger.log(
          'User has admin access and no specific spoc_ids provided - no user filtering applied',
        );
      }

      // Create query builder with correct entity relationships
      const queryBuilder = this.webinarRegistrationRepository
        .createQueryBuilder('wr')
        .leftJoinAndSelect('wr.webinar', 'webinar')
        .leftJoinAndSelect('webinar.program', 'webinarProgram')
        .leftJoinAndSelect('wr.lead_program_interest', 'leadProgramInterest')
        .leftJoinAndSelect('leadProgramInterest.lead', 'lead')
        .leftJoinAndSelect('lead.contact', 'contact')
        .leftJoinAndSelect('contact.emails', 'emails')
        .leftJoinAndSelect('contact.phones', 'phones')
        .leftJoinAndSelect('contact.primary_email', 'primaryEmail')
        .leftJoinAndSelect('contact.primary_phone', 'primaryPhone')
        .leftJoinAndSelect('contact.miles_office', 'milesOffice')
        .leftJoinAndSelect('lead.owner_spoc', 'ownerSpoc')
        .leftJoinAndSelect('ownerSpoc.spoc', 'spocUser')
        .leftJoinAndSelect('leadProgramInterest.program', 'program')
        .leftJoinAndSelect('leadProgramInterest.lead_level', 'leadLevel')
        .leftJoinAndSelect('leadProgramInterest.lead_source', 'leadSource')
        .leftJoinAndSelect('leadProgramInterest.net_enquiries', 'netEnquiries')
        .leftJoinAndSelect('wr.campaign', 'campaign');

      // Apply filters using utility
      WebinarRegistrationUtil.applyFilters(
        queryBuilder,
        {
          ...dto,
          parsedSpocIds: spocIds,
          parsedAttendStatuses: attendStatuses,
          parsedSessionStatuses: sessionStatuses,
        },
        userIds,
        hasAdminAccess,
        clientId,
      );

      // Get total count before pagination
      const total = await queryBuilder.getCount();

      // Apply pagination
      const skip = (dto.page - 1) * dto.page_size;
      queryBuilder.skip(skip).take(dto.page_size);

      // Add ordering
      queryBuilder.orderBy('wr.created_at', 'DESC');

      // Execute query
      const registrations = await queryBuilder.getMany();

      // Format data using utility
      const formattedData =
        WebinarRegistrationUtil.formatRegistrationData(registrations);

      this.logger.log(
        `Successfully fetched ${formattedData.length} webinar registrations`,
      );

      // Calculate pagination meta
      const totalPages = Math.ceil(total / dto.page_size);

      return {
        data: formattedData,
        meta: {
          total,
          page: dto.page,
          size: dto.page_size,
          totalPages,
          hasNextPage: dto.page < totalPages,
          hasPreviousPage: dto.page > 1,
        },
      };
    } catch (error) {
      this.logger.error('Error fetching webinar registrations:', error);
      throw new Error(
        `Failed to fetch webinar registrations: ${error.message}`,
      );
    }
  }
}
