import { WebinarReportData } from '../interfaces/webinar-report.interface';

export class WebinarReportUtil {
  /**
   * Formats webinar data for better readability
   * @param webinar - Raw webinar data
   * @returns Formatted webinar data
   */
  static formatWebinarData(webinar: any): WebinarReportData {
    return {
      id: webinar.id,
      title: webinar.title_on_website,
      date: webinar.date_string || null,
      time: webinar.time_string || null,
      date_time: webinar.date_time,
      program: webinar.program?.name,
      host: webinar.host,
    };
  }

  /**
   * Formats multiple webinar records
   * @param webinars - Array of raw webinar data
   * @returns Array of formatted webinar data
   */
  static formatWebinarList(webinars: any[]): WebinarReportData[] {
    return webinars.map((webinar) => this.formatWebinarData(webinar));
  }
}
