import { SelectQueryBuilder } from 'typeorm';
import {
  WebinarRegistration,
  AttendStatus,
  WebinarStatus,
} from '@modules/webinars/entities/webinar-registration.entity';
import { ExtendedWebinarRegistrationsDto } from '../dto/extended-webinar-registration.dto';
import { SessionStatus } from '../enums/webinar-report.enum';

export class WebinarRegistrationUtil {
  /**
   * Applies filters to the query builder based on the DTO
   */
  static applyFilters(
    queryBuilder: SelectQueryBuilder<WebinarRegistration>,
    dto: ExtendedWebinarRegistrationsDto,
    userIds: number[],
    hasAdminAccess: boolean,
    clientId: number,
  ): SelectQueryBuilder<WebinarRegistration> {
    // Base filters
    queryBuilder.andWhere('wr.client_id = :clientId', { clientId });
    queryBuilder.andWhere('webinar.id = :webinarId', {
      webinarId: dto.webinar_id,
    });

    // SPOC filter - only apply if user doesn't have admin access OR specific userIds are provided
    if (!hasAdminAccess || userIds.length > 0) {
      queryBuilder.andWhere('ownerSpoc.spoc_id IN (:...userIds)', { userIds });
    }

    // Pattern search (candidate_id, email, first_name, last_name, phone)
    if (dto.pattern) {
      const pattern = `%${dto.pattern.trim()}%`;
      queryBuilder.andWhere(
        `(
          LOWER(contact.candidate_id) ILIKE LOWER(:pattern) OR
          LOWER(primaryEmail.email) ILIKE LOWER(:pattern) OR
          primaryPhone.phone_number ILIKE :pattern OR
          LOWER(contact.first_name) ILIKE LOWER(:pattern) OR
          LOWER(contact.last_name) ILIKE LOWER(:pattern)
        )`,
        { pattern },
      );
    }

    // Attendance status filter - use parsed statuses
    if (dto.parsedAttendStatuses && dto.parsedAttendStatuses.length > 0) {
      queryBuilder.andWhere('wr.attendStatus IN (:...attendStatuses)', {
        attendStatuses: dto.parsedAttendStatuses,
      });
    }

    // Session status filter - use parsed session statuses
    this.applySessionStatusFilter(queryBuilder, dto.parsedSessionStatuses);

    // Webinar status filter
    if (dto.webinar_status) {
      switch (dto.webinar_status) {
        case WebinarStatus.attended:
          queryBuilder.andWhere('wr.webinar_status = :webinarStatus', {
            webinarStatus: WebinarStatus.attended,
          });
          break;
        case WebinarStatus.not_attended:
          queryBuilder.andWhere('wr.webinar_status = :webinarStatus', {
            webinarStatus: WebinarStatus.not_attended,
          });
          break;
      }
    }

    // Registration date filter
    this.applyDateFilter(
      queryBuilder,
      'wr.created_at',
      dto.registration_start_date,
      dto.registration_end_date,
    );

    // Last call date filter
    this.applyDateFilter(
      queryBuilder,
      'leadProgramInterest.last_call_date',
      dto.last_call_start_date,
      dto.last_call_end_date,
    );

    return queryBuilder;
  }

  /**
   * Applies session status filters with descriptive string values
   */
  private static applySessionStatusFilter(
    queryBuilder: SelectQueryBuilder<WebinarRegistration>,
    sessionStatuses?: SessionStatus[],
  ): void {
    if (!sessionStatuses || sessionStatuses.length === 0) return;

    const conditions: string[] = [];

    sessionStatuses.forEach((status) => {
      switch (status) {
        case SessionStatus.NOT_JOINED:
          // User registered but never joined the webinar
          conditions.push('wr.joined_at IS NULL');
          break;
        case SessionStatus.IN_SESSION:
          // User joined and is currently in the session
          conditions.push(
            '(wr.joined_at IS NOT NULL AND wr.in_session = true)',
          );
          break;
        case SessionStatus.LEFT_SESSION:
          // User joined but has left the session
          conditions.push(
            '(wr.joined_at IS NOT NULL AND wr.in_session = false)',
          );
          break;
      }
    });

    if (conditions.length > 0) {
      queryBuilder.andWhere(`(${conditions.join(' OR ')})`);
    }
  }

  /**
   * Applies date range filters
   */
  private static applyDateFilter(
    queryBuilder: SelectQueryBuilder<WebinarRegistration>,
    field: string,
    startDate?: string,
    endDate?: string,
  ): void {
    if (startDate && endDate) {
      queryBuilder.andWhere(`${field} BETWEEN :startDate AND :endDate`, {
        startDate: new Date(startDate),
        endDate: new Date(endDate),
      });
    } else if (startDate) {
      queryBuilder.andWhere(`${field} >= :startDate`, {
        startDate: new Date(startDate),
      });
    } else if (endDate) {
      queryBuilder.andWhere(`${field} <= :endDate`, {
        endDate: new Date(endDate),
      });
    }
  }

  /**
   * Masks sensitive data in emails and phones arrays
   */
  private static removeUnmasklContactData(contact: any): any {
    if (!contact) return contact;

    return {
      ...contact,
      // Mask emails array
      emails:
        contact.emails?.map((email: any) => ({
          ...email,
          email: undefined, // Remove actual email
          // Keep masked_email field
        })) || [],

      // Mask phones array
      phones:
        contact.phones?.map((phone: any) => ({
          ...phone,
          phone_number: undefined, // Remove actual phone number
          // Keep masked_phone_number field
        })) || [],

      // Mask primary email
      primary_email: contact.primary_email
        ? {
            ...contact.primary_email,
            email: undefined, // Remove actual email
            // Keep masked_email field
          }
        : null,

      // Mask primary phone
      primary_phone: contact.primary_phone
        ? {
            ...contact.primary_phone,
            phone_number: undefined, // Remove actual phone number
            // Keep masked_phone_number field
          }
        : null,
    };
  }

  /**
   * Formats webinar registration data for response
   */
  static formatRegistrationData(registrations: WebinarRegistration[]): any[] {
    return registrations.map((registration) => ({
      ...registration,
      // Remove email field from registration root level
      email: undefined,
      attendStatus: AttendStatus[registration.attendStatus],
      lead_program_interest: {
        ...registration.lead_program_interest,
        lead: {
          ...registration.lead_program_interest.lead,
          contact: this.removeUnmasklContactData(
            registration.lead_program_interest.lead?.contact,
          ),
        },
        // Sort net enquiries by creation date (newest first) and limit to 5
        net_enquiries:
          registration?.lead_program_interest?.net_enquiries
            ?.sort(
              (a, b) =>
                new Date(b.created_at).getTime() -
                new Date(a.created_at).getTime(),
            )
            .slice(0, 5) || [],
      },
    }));
  }
}
