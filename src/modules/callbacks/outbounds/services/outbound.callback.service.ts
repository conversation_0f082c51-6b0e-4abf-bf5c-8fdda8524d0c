import { CallAttendedAgentDto } from '@modules/call-logs/dto/agent-call-attended.dto';
import { CallLogService } from '@modules/call-logs/services/call-log.service';
import { Injectable } from '@nestjs/common';
import { Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import axios from 'axios';

@Injectable()
export class OutboundCallbackService {
  constructor(
    private readonly callLogService: CallLogService,
    private readonly eventEmitter: EventEmitter2,
    private configService: ConfigService,
  ) {}
  private readonly logger = new Logger(OutboundCallbackService.name);

  /**
   * Process customer call attendance with optimized data flow
   */
  async processCustomerCallAttendance(dto: CallAttendedAgentDto) {
    try {
      // Direct call to optimized service
      const updatedCall =
        await this.callLogService.updateCustomerCallAttendance(dto);

      return updatedCall;
    } catch (error) {
      Logger.error(
        `Error processing customer call attendance: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Cloud function for fetching google meet recording and attendance
   */
  async processFetchGmeetRecordingAndAttendance(
    eventId: string,
    visitId: number,
    retryCount: number,
  ) {
    const cloudFunctionPayload = {
      eventId: eventId, // Use calendar event ID or extracted meeting ID
      visitID: visitId,
      retryCount: retryCount, // Initial retry count
      callbackUrl: `${this.configService.get<string>('BASE_URL')}callbacks/google-meet-recording`,
    };

    this.logger.log(
      `Calling cloud function with payload: ${JSON.stringify(cloudFunctionPayload)}`,
    );

    // Call the cloud function to check for Google Meet recordings
    const cloudFunctionUrl =
      'https://us-central1-miles-ai-platform.cloudfunctions.net/processEventAttachments';

    // Make the HTTP request to the cloud function
    return await axios.post(cloudFunctionUrl, cloudFunctionPayload, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
  }

  /**
   * Send webhook notification for webinar registration
   * @param registration The webinar registration data
   * @returns Response from the webhook API
   */
  async sendWebinarRegistrationWebhook(
    registration: any,
    event_name?: string,
  ): Promise<any> {
    try {
      // Create webhook payload ensuring all fields are present even if null
      const webhookPayload = {
        id: registration?.id || null,
        event_name: event_name
          ? event_name
          : 'webinar_registration_confirmation',
        status: true,
        created_by: registration?.created_by || null,
        updated_by: null,
        created_at: registration?.created_at || new Date(),
        updated_at: registration?.updated_at || new Date(),
        email: registration?.email || null,
        join_url: registration?.join_url || null,
        webinar_status: registration?.webinar_status || null,
        registration_source: registration?.registration_source || null,
        webinarId: registration?.webinar?.id || null,
        webinar_date: registration?.webinar?.date_time || null,
        webinar_time: registration?.webinar?.time_string || null,
        leadId: registration?.lead?.id || null,
        campaignId: registration?.campign?.id || null,
        joined_at: registration?.joined_at || null,
        left_time: registration?.left_time || null,
        in_session: registration?.in_session || false,
        duration_in_seconds: registration?.duration_in_seconds || 0,
        attendStatus: registration?.attendStatus || null,
      };

      const webhookUrl =
        this.configService.get<string>('WEBINAR_WEBHOOK_URL') ||
        'https://webhook.miles-api.com/webhook/webinar-registration-successful';

      const response = await axios.post(webhookUrl, webhookPayload, {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      Logger.log(
        `Webhook notification sent for webinar registration: ${response.status}`,
        'OutboundCallbackService',
      );

      //TODO Log success to MongoDB or any logging service

      return response.data;
    } catch (error) {
      //TODO Log error to MongoDB or any logging service

      Logger.error(
        `Error sending webinar registration webhook: ${error.message}`,
        error.stack,
      );
      throw new Error(
        `Failed to send webinar registration webhook: ${error.message}`,
      );
    }
  }
  async sendMessageEvent(payload: any) {
    const url = this.configService.get<string>('WHATSAPP_EMAIL_MESSSAGE_URL');
    const response = await axios.post(url, payload, {
      headers: {
        'Content-Type': 'application/json',
      },
    });
    console.log('response from whatsapp email service', response.data);
  }
}
