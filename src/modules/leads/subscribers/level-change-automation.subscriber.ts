import { Injectable, Logger } from '@nestjs/common';
import {
  DataSource,
  EntitySubscriberInterface,
  EventSubscriber,
  UpdateEvent,
} from 'typeorm';
import { LeadProgramInterest } from '../entities/lead-program-interest.entity';
import { UserContextService } from '../../../common/service/user-context.service';
import { LeadLevelService } from '../services/lead-level.service';
import { EnrollmentService } from '@modules/enrollments/services/enrollment.service';
import { EnrollmentStage } from '../enums/lead-level.enum';

@Injectable()
@EventSubscriber()
export class LevelChangeAutomationSubscriber
  implements EntitySubscriberInterface<LeadProgramInterest>
{
  private readonly logger = new Logger(LevelChangeAutomationSubscriber.name);

  constructor(
    private readonly leadLevelService: LeadLevelService,
    private readonly enrollmentService: EnrollmentService,
    dataSource: DataSource,
  ) {
    dataSource.subscribers.push(this);
  }

  /**
   * Indicates that this subscriber only listen to LeadProgramInterest events
   */
  listenTo() {
    return LeadProgramInterest;
  }

  /**
   * Called after entity update
   */
  //!! This only works with save() method, not with update()
  async afterUpdate(event: UpdateEvent<LeadProgramInterest>): Promise<void> {
    // Check if lead_level_id was changed

    if (
      event.updatedColumns.some(
        (column) => column.propertyName === 'lead_level_id',
      )
    ) {
      const oldLevelId = event.databaseEntity?.lead_level_id;
      const newLevelId = event.entity?.lead_level_id;

      // Only trigger if level actually changed
      if (oldLevelId !== newLevelId) {
        // Get current user context
        const userContext = UserContextService.getContext();

        // 🔧 Fetch the updated entity from database to get the actual current state
        const updatedEntity = await event.manager.findOne(LeadProgramInterest, {
          where: { id: event.entity.id },
          relations: [
            'lead_level',
            'program',
            'lead',
            'lead.owner_spoc',
            'lead.owner_spoc.spoc',
          ],
        });

        if (!updatedEntity) {
          this.logger.warn(
            `Could not find updated LeadProgramInterest with ID ${event.entity.id}`,
          );
          return;
        }

        this.logger.log(
          `Level change detected for LeadProgramInterest ${updatedEntity.id}: ` +
            `${oldLevelId} → ${updatedEntity.lead_level_id}` +
            (userContext
              ? ` by user ${userContext.userId} (${userContext.userName})`
              : ' by unknown user'),
        );

        // Trigger your automation function with the actual updated data
        await this.triggerLevelChangeAutomation({
          leadProgramInterestId: updatedEntity.id,
          fromLevelId: oldLevelId,
          toLevelId: updatedEntity.lead_level_id, // Use the actual DB value
          leadId: updatedEntity.lead_id,
          programId: updatedEntity.program_id,
          // Include user information
          changedByUserId: userContext?.userId,
          changedByUserName: userContext?.userName,
          changedByUserEmail: userContext?.userEmail,
          action: userContext?.action,
          // Include the full updated entity for more context
          updatedEntity: updatedEntity,
        });
      }
    }
  }

  /**
   * Your automation logic goes here
   */
  private async triggerLevelChangeAutomation(params: {
    leadProgramInterestId: number;
    fromLevelId: number | null;
    toLevelId: number | null;
    leadId: number;
    programId: number;
    // User context
    changedByUserId?: number;
    changedByUserName?: string;
    changedByUserEmail?: string;
    action?: string;
    updatedEntity?: LeadProgramInterest;
  }): Promise<void> {
    const {
      leadProgramInterestId,
      fromLevelId,
      toLevelId,
      leadId,
      programId,
      changedByUserId,
      changedByUserName,
      action,
    } = params;

    try {
      this.logger.log(`🚀 Executing level change automation for:
        - Lead Program Interest: ${leadProgramInterestId}
        - Lead: ${leadId}
        - Program: ${programId}
        - Level Change: ${fromLevelId} → ${toLevelId}
        - Changed by: ${changedByUserName} (ID: ${changedByUserId})
        - Spoc : ${params.updatedEntity?.lead?.owner_spoc?.spoc?.first_name || 'N/A'}
        - User Email: ${params.changedByUserEmail}
        - Action: ${action}`);

      // ✅ Add your automation logic here:

      //! Handle only for DPP for NOW
      if (programId !== 1) {
        return;
      }

      //fetch the level
      const leadLevel = await this.leadLevelService.findById(toLevelId);
      if (
        !leadLevel ||
        leadLevel.enrollment_stage !== EnrollmentStage.Enrolled
      ) {
        this.logger.warn(
          `⚠️ Level ${toLevelId} is not in ENROLLED stage, skipping enrollment handling.`,
        );
        return;
      }

      await this.handleEnrollment({
        leadProgramInterestId,
        spocId: params.updatedEntity?.lead?.owner_spoc?.spoc?.id,
        updatedEntity: params.updatedEntity, // Pass the fresh entity
      });

      this.logger.log(`✅ Level change automation completed successfully`);
    } catch (error) {
      this.logger.error(`❌ Level change automation failed:`, error);
      // Don't throw error to avoid breaking the main update operation
    }
  }

  private async handleEnrollment(params: {
    leadProgramInterestId: number;
    spocId: number;
    // Add the fresh entity to pass along
    updatedEntity: LeadProgramInterest;
  }) {
    this.logger.log(
      `🔍 Handling enrollment for leadProgramInterest ${params.leadProgramInterestId}...`,
    );
    try {
      await this.enrollmentService.handleEnrollment({
        lead_program_interest_id: params.leadProgramInterestId,
        enrolled_by_id: params.spocId,
        enrollment_date: new Date(),
        // Pass the fresh entity data to avoid stale cache issues
        fresh_entity_data: params.updatedEntity,
      });
    } catch (error) {
      this.logger.error(
        `❌ Error handling enrollment for leadProgramInterestId ${params.leadProgramInterestId}:`,
        error,
      );
      // Handle error gracefully, maybe log it or notify admins
    }
  }
}
