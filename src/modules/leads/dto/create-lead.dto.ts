import {
  IsBoolean,
  IsNotEmpty,
  IsN<PERSON>ber,
  IsObject,
  IsOptional,
  IsString,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { CreateContactDto } from 'src/modules/contacts/dto/create-contact.dto';

export class CreateLeadDto {
  @ApiProperty({
    type: () => CreateContactDto,
    description: 'Contact information for the lead',
  })
  @IsNotEmpty()
  contact_info: CreateContactDto;

  @ApiPropertyOptional({ description: 'Optional notes about the lead' })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiProperty({ description: 'Client ID associated with the lead' })
  @IsNotEmpty()
  @IsNumber()
  client_id: number;

  @ApiPropertyOptional({
    description: 'Optional metadata object with additional info',
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;

  @ApiPropertyOptional({ description: 'Do Not Disturb flag' })
  @IsOptional()
  @IsBoolean()
  is_DND?: boolean;
}

export class createLeadWithRelationsDto extends CreateLeadDto {
  @ApiProperty({
    description: 'ID of the owner SPOC (Single Point of Contact)',
  })
  @IsNumber()
  level_id: number;

  @ApiProperty({ description: 'Campaign Id' })
  @IsOptional()
  @IsNumber()
  campaign_id: number;

  @ApiProperty()
  @IsNumber()
  program_id: number;

  @ApiProperty({
    description: 'Phone Id',
  })
  @IsNumber()
  @IsOptional()
  phone_id?: number;

  @ApiProperty({
    description: 'Call Log Id',
  })
  @IsNumber()
  @IsOptional()
  call_log_id?: number;

  @ApiPropertyOptional({ description: 'Date for the next follow-up' })
  @IsOptional()
  next_followup_date?: string;
}
