import {
  IsBoolean,
  IsDate,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { LeadCallStatus } from '../enums/lead-status.enum';

export class CreateLeadProgramInterestDto {
  @ApiProperty({ description: 'ID of the lead' })
  @IsNotEmpty()
  @IsNumber()
  lead_id: number;

  @ApiProperty({ description: 'ID of the client' })
  @IsNotEmpty()
  @IsNumber()
  client_id: number;

  @ApiProperty({ description: 'ID of the program' })
  @IsNotEmpty()
  @IsNumber()
  lead_level_id: number;

  @IsNotEmpty()
  @IsNumber()
  program_id: number;

  @ApiPropertyOptional({ description: 'ID of the lead source' })
  @IsOptional()
  @IsNumber()
  lead_source_id?: number;

  @ApiPropertyOptional({ description: 'Notes about the lead program interest' })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiPropertyOptional({
    description: 'Flag indicating if this is the primary program interest',
  })
  @IsOptional()
  @IsBoolean()
  is_primary?: boolean;

  @ApiPropertyOptional({
    description: 'Additional metadata as key-value pairs',
    type: 'object',
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;

  @ApiPropertyOptional({
    description: 'Date of re-enquiry',
    type: String,
    format: 'date-time',
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  re_enquiry_date?: Date;

  @ApiPropertyOptional({
    description: 'Date of last call',
    type: String,
    format: 'date-time',
  })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  last_call_date?: Date;

  @ApiPropertyOptional({
    enum: LeadCallStatus,
    description: 'Status of the lead call',
  })
  @IsOptional()
  @IsEnum(LeadCallStatus)
  lead_call_status?: LeadCallStatus;

  @ApiPropertyOptional({ description: 'ID of the SPOC assigned' })
  @IsOptional()
  @IsNumber()
  spoc_id?: number;
}
