import { PhoneType } from '@modules/contacts/enums/contact.enum';
import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
} from 'class-validator';
import { LeadPhonePatchType } from '../enums/phone-lead-patch-type.enum';

export class HandleUpdatePhoneTypeDto {
  @ApiProperty({ description: 'ID of the lead' })
  @IsOptional()
  @IsNumber()
  lead_id: number;

  @ApiProperty({ description: 'ID of the phone' })
  @IsOptional()
  @IsNumber()
  phone_id: number;

  @ApiProperty({ description: 'New phone type' })
  @IsOptional()
  phone_type: PhoneType;

  @ApiProperty({ description: 'Do Not Disturb flag' })
  @IsOptional()
  @IsBoolean()
  is_DND?: boolean;

  @ApiProperty({
    description: 'Type of patch update',
    enum: LeadPhonePatchType,
    enumName: 'PhonePatchType',
  })
  @IsNotEmpty()
  @IsEnum(LeadPhonePatchType)
  patch_type: LeadPhonePatchType;
}
