import {
  <PERSON><PERSON><PERSON>al,
  <PERSON><PERSON><PERSON>y,
  IsDateString,
  IsNumber,
  IsString,
  IsEnum,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { BaseDto } from 'src/common/dto/base.dto';
import { ApiProperty } from '@nestjs/swagger';
import { WebinarStatus } from 'src/modules/webinars/entities/webinar-registration.entity';
import { LeadType } from '../enums/lead-level.enum';
import { LeadSessionStatus } from '@modules/session/enums/lead-session-status.enum';
import { InterviewStatus } from '@modules/interviews/enums/interview-status.enum';
import { EnrollmentStatus } from '@modules/enrollments/types/enrollment-status.type';

export class GetLeadsQueryDto extends BaseDto {
  @ApiProperty({
    required: false,
    default: 1,
    description: 'Page number for pagination',
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  page?: number = 1;

  @ApiProperty({
    required: false,
    default: 10,
    description: 'Number of items per page',
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  size?: number = 10;

  @ApiProperty({
    required: false,
    type: [Number],
    description: 'Filter leads by city IDs',
  })
  @IsOptional()
  @Transform(({ value }) =>
    typeof value === 'string' ? [+value] : value?.map((v) => +v),
  )
  @IsArray()
  city_ids?: number[];

  @ApiProperty({
    required: false,
    type: [Number],
    description: 'Filter leads by program IDs',
  })
  @IsOptional()
  @Transform(({ value }) =>
    typeof value === 'string' ? [+value] : value?.map((v) => +v),
  )
  @IsArray()
  program_ids?: number[];

  @ApiProperty({
    required: false,
    description: 'Start date for lead creation filter (ISO 8601 format)',
  })
  @IsOptional()
  @IsDateString()
  lead_created_start_date?: string;

  @ApiProperty({
    required: false,
    description: 'End date for lead creation filter (ISO 8601 format)',
  })
  @IsOptional()
  @IsDateString()
  lead_created_end_date?: string;

  @ApiProperty({
    required: false,
    description: 'Start date for last call filter (ISO 8601 format)',
  })
  @IsOptional()
  @IsDateString()
  last_call_start_date?: string;

  @ApiProperty({
    required: false,
    description: 'End date for last call filter (ISO 8601 format)',
  })
  @IsOptional()
  @IsDateString()
  last_call_end_date?: string;

  @ApiProperty({
    required: false,
    description: 'Start date for last connected call filter (ISO 8601 format)',
  })
  @IsOptional()
  @IsDateString()
  last_connected_call_start_date?: string;

  @ApiProperty({
    required: false,
    description: 'End date for last connected call filter (ISO 8601 format)',
  })
  @IsOptional()
  @IsDateString()
  last_connected_call_end_date?: string;

  @ApiProperty({
    required: false,
    description: 'Start date for next followup filter (ISO 8601 format)',
  })
  @IsOptional()
  @IsDateString()
  next_followup_start_date?: string;

  @ApiProperty({
    required: false,
    description: 'End date for next followup filter (ISO 8601 format)',
  })
  @IsOptional()
  @IsDateString()
  next_followup_end_date?: string;

  @ApiProperty({
    required: false,
    type: [Number],
    description: 'Filter leads by SPOC IDs',
  })
  @IsOptional()
  @Transform(({ value }) =>
    typeof value === 'string' ? [+value] : value?.map((v) => +v),
  )
  @IsArray()
  spoc_ids?: number[];

  @ApiProperty({
    required: false,
    type: [Number],
    description: 'Filter leads by campaign IDs',
  })
  @IsOptional()
  @Transform(({ value }) =>
    typeof value === 'string' ? [+value] : value?.map((v) => +v),
  )
  @IsArray()
  campaign_ids?: number[];

  @ApiProperty({
    required: false,
    type: [Number],
    description: 'Filter leads by level IDs',
  })
  @IsOptional()
  @Transform(({ value }) =>
    typeof value === 'string' ? [+value] : value?.map((v) => +v),
  )
  @IsArray()
  level_ids?: number[];

  @ApiProperty({
    required: false,
    description: 'Filter by lead types (NE: New, RE: Returning)',
    enum: LeadType,
    isArray: true,
    example: ['ne', 're'],
  })
  @IsOptional()
  @IsEnum(LeadType, { each: true })
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return [value]; // Convert single string to array with one element
    } else if (Array.isArray(value)) {
      return value;
    }
    return undefined; // Handle undefined/null
  })
  @IsArray()
  lead_type?: LeadType[];

  @ApiProperty({
    required: false,
    description: 'Filter by stage 1 counselling status',
  })
  @IsOptional()
  @IsEnum(LeadSessionStatus)
  s1_counselling_status?: LeadSessionStatus;

  @ApiProperty({
    required: false,
    description: 'Filter by stage 2 counselling status',
  })
  @IsOptional()
  @IsEnum(LeadSessionStatus)
  s2_counselling_status?: LeadSessionStatus;

  @ApiProperty({
    required: false,
    enum: WebinarStatus,
    description:
      'Filter by webinar status (attended, not_attended, registered, or cancelled)',
  })
  @IsOptional()
  @IsEnum(WebinarStatus)
  past_webinar_status?: WebinarStatus;

  @ApiProperty({
    required: false,
    enum: WebinarStatus,
    description:
      'Filter by webinar status (attended, not_attended, registered, or cancelled)',
  })
  @IsOptional()
  @IsEnum(WebinarStatus)
  upcoming_webinar_status?: WebinarStatus;

  @ApiProperty({
    required: false,
    description: 'Filter by interview status',
    enum: InterviewStatus,
  })
  @IsOptional()
  @IsEnum(InterviewStatus)
  interview_status?: InterviewStatus;

  @ApiProperty({
    required: false,
    type: [Number],
    description: 'Filter leads by university IDs',
  })
  @IsOptional()
  @Transform(({ value }) =>
    typeof value === 'string' ? [+value] : value?.map((v) => +v),
  )
  @IsArray()
  university_ids?: number[];

  @ApiProperty({ required: false, description: 'Filter by application status' })
  @IsOptional()
  @IsString()
  application_status?: string;

  @ApiProperty({
    required: false,
    description: 'Start date for enrollment filter (ISO 8601 format)',
  })
  @IsOptional()
  @IsDateString()
  enrollment_start_date?: string;

  @ApiProperty({
    required: false,
    description: 'End date for enrollment filter (ISO 8601 format)',
  })
  @IsOptional()
  @IsDateString()
  enrollment_end_date?: string;

  @ApiProperty({
    required: false,
    description: 'Filter by enrollment status',
    enum: EnrollmentStatus,
  })
  @IsOptional()
  @IsEnum(EnrollmentStatus)
  enrollment_status?: EnrollmentStatus;
}
