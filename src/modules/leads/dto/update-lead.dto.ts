import { PartialType } from '@nestjs/mapped-types';
import { CreateLeadDto } from './create-lead.dto';
import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsNumber, IsOptional } from 'class-validator';

export class UpdateLeadDto extends PartialType(CreateLeadDto) {
  @ApiProperty({ description: 'Do Not Disturb flag' })
  @IsOptional()
  @IsBoolean()
  is_DND?: boolean;
}

export class UpdateLeadMetaDto {
  @ApiProperty({
    type: 'object',
    description: 'Meta details to be updated for the lead',
    example: {
      key1: 'value1',
      key2: 'value2',
    },
  })
  @IsNotEmpty()
  metaDetails: Record<string, any>;

  @ApiProperty({
    type: 'number',
    description: 'Lead ID of the person updating the lead',
    example: 123,
  })
  @IsNumber()
  @IsNotEmpty()
  leadId: number;
}

export class UpdateLeadLevelDto {
  @ApiProperty({
    type: 'number',
    description: 'ID of the lead program interest to update',
    example: 1,
  })
  @IsNumber()
  @IsNotEmpty()
  leadProgInterestId: number;

  @ApiProperty({
    type: 'number',
    description: 'Current level ID of the lead',
    example: 2,
  })
  @IsNumber()
  @IsNotEmpty()
  currentLevelId: number;

  @ApiProperty({
    type: 'number',
    description: 'New level ID to transition the lead to',
    example: 3,
  })
  @IsNumber()
  @IsNotEmpty()
  newLevelId: number;

  @ApiProperty({
    type: 'string',
    description: 'Next follow-up date in ISO format (optional)',
    example: '2023-10-01T10:00:00Z',
    required: false,
  })
  @IsOptional()
  nextFollowUpDate?: string | null;

  @ApiProperty({
    type: 'string',
    description: 'Comments or notes for the lead level update (optional)',
    example: 'Updated to new level based on interest',
  })
  @IsOptional()
  comments?: string;
}

export class UpdateNameDto {
  @ApiProperty({
    type: 'number',
    description: 'Lead ID of the person updating the lead',
    example: 123,
  })
  @IsNumber()
  @IsNotEmpty()
  leadId: number;

  @ApiProperty({
    type: 'string',
    description: 'First name of the lead',
    example: 'John',
  })
  @IsNotEmpty()
  firstName: string;

  @ApiProperty({
    type: 'string',
    description: 'Last name of the lead (optional)',
    example: 'Doe',
    required: false,
  })
  @IsOptional()
  lastName?: string;
}
