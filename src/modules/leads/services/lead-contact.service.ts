import { ContactService } from '@modules/contacts/services/contact.service';
import {
  BadRequestException,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { Lead } from '../entities/lead.entity';
import { LeadService } from './lead.service';
import { InjectRepository } from '@nestjs/typeorm';
import { Phone } from '@modules/contacts/entities/phone.entity';
import { Repository } from 'typeorm';
import { CallLogService } from '@modules/call-logs/services/call-log.service';
import { HandleUpdatePhoneTypeDto } from '../dto/update-lead-phone-type.dto';
import { LeadPhonePatchType } from '../enums/phone-lead-patch-type.enum';
import { LeadProgramInterest } from '../entities/lead-program-interest.entity';
import { LeadLevel } from '../entities/lead-level.entity';
import {
  AllowedTransitionMethodEnum,
  EnrollmentStage,
} from '../enums/lead-level.enum';
import { PhoneType } from '@modules/contacts/enums/contact.enum';
import { LeadLevelService } from './lead-level.service';
import { LeadsHistoryService } from './leads-history.service';

@Injectable()
export class LeadContactService {
  constructor(
    private readonly contactService: ContactService,
    private readonly leadService: LeadService,
    @InjectRepository(Phone)
    private readonly phoneRepository: Repository<Phone>,
    private readonly callLogService: CallLogService,
    @InjectRepository(LeadProgramInterest)
    private readonly leadProgramInterestRepository: Repository<LeadProgramInterest>,
    @InjectRepository(LeadLevel)
    private readonly leadLevel: Repository<LeadLevel>,
    private readonly leadLevelService: LeadLevelService,
    private readonly leadsHistoryService: LeadsHistoryService,
  ) {}

  async updateContactMetaDetails(
    leadId: number,
    userId: number,
    metaDetails: Record<string, any>,
  ): Promise<{ data: Lead; success: boolean }> {
    try {
      const lead = await this.leadService.findOne(leadId);

      if (!lead) {
        throw new Error(`Lead with ID ${leadId} not found`);
      }

      const contactId = lead.contact_id;
      if (!contactId) {
        throw new Error('Contact ID is required to update meta details');
      }

      await this.contactService.updateMetadata(metaDetails, contactId);

      if (!metaDetails || typeof metaDetails !== 'object') {
        throw new Error('Meta details must be a valid object');
      }

      return {
        data: lead,
        success: true,
      };
      // Update the contact's metadata
    } catch (error) {
      console.error('Error updating contact meta details:', error);
      throw new Error('Failed to update contact meta details');
    }
  }

  async handleContactNameUpdate(
    leadId: number,
    userId: number,
    firstName: string,
    lastName?: string,
  ): Promise<Lead> {
    try {
      const lead = await this.leadService.findOne(leadId);

      if (!lead) {
        throw new Error(`Lead with ID ${leadId} not found`);
      }

      const contactId = lead.contact_id;
      if (!contactId) {
        throw new Error('Contact ID is required to update name');
      }

      // Update the contact's name
      await this.contactService.updateContactName({
        firstName,
        lastName,
        contactId,
        userId,
      });

      // Refetch the lead to get the updated contact information
      return await this.leadService.findOne(leadId);
    } catch (error) {
      console.error('Error updating contact name:', error);
      throw new Error('Failed to update contact name');
    }
  }

  async viewEmail(emailId: number, userId: number, clientId: number) {
    const email = await this.contactService.getContactFromEmailId(emailId);
    const lead = await this.leadService.findByClientAndContact(
      clientId,
      email?.contact?.id,
    );
    return this.contactService.viewEmail(email, userId, lead?.id);
  }

  async viewPhone(phoneId: number, userId: number, clientId: number) {
    const phone = await this.contactService.getContactFromPhoneId(phoneId);
    const lead = await this.leadService.findByClientAndContact(
      clientId,
      phone?.contact?.id,
    );
    return this.contactService.viewPhone(phone, userId, lead?.id);
  }

  async addPhoneToLeadContact(
    phoneId: number,
    leadId: number,
    callLogId?: number,
    userId?: number,
  ) {
    try {
      if (callLogId) {
        const callLog = await this.callLogService.findOneById(callLogId);
        if (!callLog) {
          throw new NotFoundException(
            `Call log with ID ${callLogId} not found`,
          );
        }
        if (callLog.phone.id !== phoneId) {
          throw new BadRequestException(
            `Phone with ID ${phoneId} does not match the phone in call log ${callLogId}`,
          );
        }
      }

      const lead = await this.leadService.findOne(leadId);
      if (!lead) {
        throw new NotFoundException(`Lead with ID ${leadId} not found`);
      }

      const contactId = lead.contact_id;
      if (!contactId) {
        throw new Error('Contact ID is required to add phone');
      }

      const phone = await this.phoneRepository.findOne({
        where: { id: phoneId },
      });

      if (!phone) {
        throw new NotFoundException(
          `Phone with ID ${phoneId} not found for contact ID ${contactId}`,
        );
      }

      if (phone.contact_id) {
        throw new Error(
          `Phone with ID ${phoneId} is already associated with a contact.`,
        );
      }

      await this.contactService.attachPhoneToContact(
        phoneId,
        contactId,
        userId,
      );

      //finally attach the lead to the call log
      if (callLogId) {
        try {
          await this.callLogService.assignleadToUntrackedCalls(lead, callLogId);
        } catch (error) {
          console.error('Error assigning lead to call log:', error);
          throw new Error('Failed to assign lead to call log');
        }
      }

      return await this.leadService.findOne(leadId);
    } catch (error) {
      console.error('Error adding phone to lead contact:', error);
      throw new BadRequestException(
        error.message || 'Failed to add phone to lead contact',
      );
    }
  }

  /**
   * Handles the update phone type for a contact.
   * Handles updating DND status for lead
   */
  async handleUpdatePhoneType(dto: HandleUpdatePhoneTypeDto, userId: number) {
    const { lead_id, phone_id, phone_type, is_DND, patch_type } = dto;
    let lead = null;

    try {
      // Handle phone type updates (spam/employee/lead markers)
      if (
        patch_type === LeadPhonePatchType.SPAM ||
        patch_type === LeadPhonePatchType.EMPLOYEE ||
        patch_type === LeadPhonePatchType.LEAD
      ) {
        // Validate that phone_type matches patch_type
        if (
          (patch_type === LeadPhonePatchType.SPAM &&
            phone_type !== PhoneType.spam) ||
          (patch_type === LeadPhonePatchType.EMPLOYEE &&
            phone_type !== PhoneType.miles_employee) ||
          (patch_type === LeadPhonePatchType.LEAD &&
            phone_type !== PhoneType.lead)
        ) {
          throw new BadRequestException(
            `Invalid phone type for the requested action. Expected phone_type to match patch_type.`,
          );
        }

        // Find phone
        const phone = await this.phoneRepository.findOne({
          where: { id: phone_id },
          relations: {
            contact: { phones: true },
          },
        });

        if (!phone) {
          throw new NotFoundException(`Phone with ID ${phone_id} not found`);
        }

        const updateDto = {
          phoneId: phone.id,
          phoneType: phone_type,
        };

        Logger.log(
          `Updating phone ${phone.id} to type: ${phone_type} as per patch type: ${patch_type}`,
        );

        const updatePhoneType = await this.contactService.updatePhoneType(
          { ...updateDto },
          userId,
        );

        // Only proceed with lead level update if lead_id is provided
        if (lead_id && phone.contact) {
          Logger.log(`Lead ID provided, checking for lead level downgrade...`);

          // Find lead since it was provided
          lead = await this.leadService.findOne(lead_id);
          if (!lead) {
            throw new NotFoundException(`Lead with ID ${lead_id} not found`);
          }
          Logger.log(`Found lead ID: ${lead_id}`);

          if (lead.contact_id !== phone.contact.id) {
            throw new BadRequestException(
              `Lead contact ID ${lead.contact_id} does not match phone contact ID ${phone.contact.id}`,
            );
          }

          const contactId = lead.contact_id;
          if (!contactId) {
            throw new Error('Contact ID is required to update phone type');
          }
          Logger.log(`Found contact: ${contactId} for lead: ${lead_id}`);

          const phonesCount = phone.contact.phones?.length || 0;
          Logger.log(`Contact ${phone.contact.id} has ${phonesCount} phone(s)`);

          if (
            phonesCount === 1 &&
            updatePhoneType &&
            patch_type !== LeadPhonePatchType.LEAD
          ) {
            Logger.log(
              `Lead has only one phone. Downgrading lead level across all program interests...`,
            );

            // Find ALL program interests for this lead
            const leadProgramInterests =
              await this.leadProgramInterestRepository.find({
                where: { lead_id: lead_id },
              });

            if (leadProgramInterests.length === 0) {
              Logger.log(`No program interests found for lead ${lead_id}`);
              throw new NotFoundException(
                `No program interests found for lead ${lead_id}`,
              );
            }

            Logger.log(
              `Found ${leadProgramInterests.length} program interests to update`,
            );

            // Track updates for history creation
            const updatePromises = [];
            const historyPromises = [];

            // Process each program interest
            for (const programInterest of leadProgramInterests) {
              // Find the lowest negative level for this specific program
              const lowestNegativeLevel = await this.leadLevel.findOne({
                where: {
                  client_id: programInterest.client_id,
                  program_id: programInterest.program_id,
                  is_positive: false,
                  enrollment_stage: EnrollmentStage.PreEnroll,
                },
                order: {
                  display_order: 'ASC', // Ensure we get the lowest level even if display_order isn't 1
                },
              });

              if (lowestNegativeLevel) {
                Logger.log(
                  `Found lowest negative level ${lowestNegativeLevel.name} (ID: ${lowestNegativeLevel.id}) for program: ${programInterest.program_id}`,
                );

                // Store data for history
                const programInterestId = programInterest.id;
                const fromLevelId = programInterest.lead_level_id;
                const toLevelId = lowestNegativeLevel.id;

                // Skip update if already at this level
                if (fromLevelId === toLevelId) {
                  Logger.log(
                    `Program interest ${programInterestId} already at level ${toLevelId}, skipping update`,
                  );
                  continue;
                }

                // Add update to our batch
                updatePromises.push(
                  this.leadProgramInterestRepository
                    .update(
                      { id: programInterest.id },
                      {
                        lead_level_id: toLevelId,
                        updated_by: userId,
                      },
                    )
                    .then((result) => {
                      if (result.affected > 0) {
                        Logger.log(
                          `Updated program interest ${programInterest.id} from level ${fromLevelId} to ${toLevelId}`,
                        );

                        // Only create history if update was successful
                        historyPromises.push(
                          this.leadLevelService.createLevelChangeHistory({
                            programInterestId,
                            fromLevelId,
                            toLevelId,
                            userId,
                            transitionMethod:
                              AllowedTransitionMethodEnum.Manual,
                            comments: `Lead level downgraded due to phone marked as ${phone_type}`,
                          }),
                        );
                      } else {
                        Logger.log(
                          `Failed to update program interest ${programInterest.id}`,
                        );
                      }
                    }),
                );
              } else {
                Logger.log(
                  `No suitable negative lead level found for program ${programInterest.program_id} in client ${programInterest.client_id}`,
                );
              }
            }

            // Execute all updates
            await Promise.all(updatePromises);

            // Execute all history creations
            await Promise.all(historyPromises);

            Logger.log(
              `Completed lead level downgrades for all program interests of lead ${lead_id}`,
            );
          } else {
            Logger.log(
              `Contact ${phone.contact.id} has multiple phones or no update was made, not downgrading the level`,
            );
          }
        } else {
          Logger.log(
            `No lead ID provided or no contact found for phone, skipping lead level downgrade`,
          );
        }
        return { success: true, message: 'Updated Successfully' };
      }

      if (patch_type === LeadPhonePatchType.DND) {
        // Handle DND status update - lead_id is required
        if (!lead_id) {
          Logger.log(`Missing lead_id for DND update`);
          throw new BadRequestException('Lead ID is required for DND updates');
        }

        // Find lead for DND update
        lead = await this.leadService.findOne(lead_id);
        if (!lead) {
          Logger.log(`Lead not found: ${lead_id}`);
          throw new NotFoundException(`Lead with ID ${lead_id} not found`);
        }

        const previousDndStatus = lead.is_DND;

        if (lead.is_DND === is_DND) {
          Logger.log(
            `Lead ${lead_id} already has DND status set to ${is_DND}, no update needed`,
          );
          throw new BadRequestException(
            `Lead already has DND status set to ${is_DND}, no change required`,
          );
        }

        Logger.log(`Updating lead ID ${lead_id} DND status to: ${is_DND}`);
        const updateDnd = await this.leadService.update(
          lead_id,
          {
            is_DND: is_DND,
          },
          userId,
        );

        const historyDetails = {
          actionType: updateDnd.is_DND ? 'Marked as DND' : 'Removed from DND',
          previousStatus: String(previousDndStatus),
          newStatus: String(updateDnd.is_DND),
          leadId: lead_id,
          contactId: lead.contact_id,
        };
        // Create lead DND history
        await this.leadsHistoryService.createLeadDndHistory(
          historyDetails,
          userId,
        );

        return { success: true, message: 'Updated Successfully' };
      }

      throw new BadRequestException(
        `Invalid patch type: ${patch_type}. Must be one of ${Object.values(
          LeadPhonePatchType,
        ).join(', ')}`,
      );
    } catch (error) {
      throw new BadRequestException(
        error.message || 'Failed to update phone type',
      );
    }
  }
}
