import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { LeadLevel } from '../entities/lead-level.entity';
import {
  ReEnquiryAction,
  AllowedTransitionMethodEnum,
  EnrollmentStage,
} from '../enums/lead-level.enum';
import { PaginatedResponse } from 'src/common/interfaces/pagination.interface';
import { LeadProgramInterest } from '../entities/lead-program-interest.entity';
import { Lead } from '../entities/lead.entity';
import { LeadLevelHistory } from '../entities/lead-level-history.entity';
import { getNextFollowUpDateByLevel } from '../utils/next-follow-date';
import { LeadHistoryService } from '@modules/lead-histories/services/lead-history.service';
import {
  ActionType,
  EngagementSubActionType,
} from '@modules/lead-histories/enums/lead-history.enum';
import { UserService } from '@modules/users/services/user.service';
import { DispositionHistory } from '@modules/dispositions/entities/disposition-history.entity';

@Injectable()
export class LeadLevelService {
  private readonly logger = new Logger(LeadLevelService.name);

  constructor(
    @InjectRepository(LeadLevel)
    private readonly leadLevelRepository: Repository<LeadLevel>,
    @InjectRepository(LeadProgramInterest)
    private readonly leadProgramInterestRepo: Repository<LeadProgramInterest>,
    @InjectRepository(Lead)
    private readonly leadRepository: Repository<Lead>,
    @InjectRepository(DispositionHistory)
    private readonly dispositionHistoryRepository: Repository<DispositionHistory>,
    @InjectRepository(LeadLevelHistory)
    private readonly leadLevelHistoryRepository: Repository<LeadLevelHistory>,
    @Inject(forwardRef(() => LeadHistoryService))
    private readonly leadHistoryService: LeadHistoryService,
    private readonly userService: UserService,
  ) {}

  /**
   * Find the default level for new enquiries for a specific client
   * @param clientId - ID of the client
   * @returns Default lead level or null if not found
   */
  async findDefaultForNewEnquiry(
    clientId: number,
    programId: number,
  ): Promise<LeadLevel | null> {
    try {
      const defaultLevel = await this.leadLevelRepository.findOne({
        where: {
          client_id: clientId,
          program_id: programId,
          is_default_for_ne: true,
        },
      });

      if (!defaultLevel) {
        this.logger.warn(`No default lead level found for client ${clientId}`);
        return null;
      }

      return defaultLevel;
    } catch (error) {
      this.logger.error(
        `Error finding default lead level: ${error.message}`,
        error.stack,
      );
      return null;
    }
  }

  /**
   * Find a lead level by ID
   * @param id - ID of the lead level
   * @returns Lead level or throws NotFoundException if not found
   */
  async findById(id: number): Promise<LeadLevel> {
    try {
      const level = await this.leadLevelRepository.findOne({
        where: { id },
      });

      if (!level) {
        throw new NotFoundException(`Lead level with ID ${id} not found`);
      }

      return level;
    } catch (error) {
      this.logger.error(
        `Error finding lead level by ID ${id}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Handle re-enquiry level transition based on current level's configuration
   * @param currentLevelId - ID of the current lead level
   * @param clientId - ID of the client
   * @returns Target level ID after handling re-enquiry, or null if no action
   */
  async handleReEnquiryLevelTransition(
    currentLevelId: number,
    clientId: number,
  ): Promise<number | null> {
    try {
      // Get the current level to check re-enquiry action
      const currentLevel = await this.leadLevelRepository.findOne({
        where: { id: currentLevelId, client_id: clientId },
      });

      if (!currentLevel) {
        this.logger.warn(`Lead level with ID ${currentLevelId} not found`);
        return null;
      }

      // Handle different re-enquiry actions
      switch (currentLevel.re_enquiry_action) {
        case ReEnquiryAction.NoAction:
          // Keep the current level
          return currentLevelId;

        case ReEnquiryAction.AutoTransitionToSpecific:
          // Transition to the specified target level if available
          if (currentLevel.re_enquiry_target_level_id) {
            this.logger.log(
              `Auto transitioning from level ${currentLevelId} to ${currentLevel.re_enquiry_target_level_id} due to re-enquiry`,
            );
            return currentLevel.re_enquiry_target_level_id;
          }
          // If target not set, stay in current level
          return currentLevelId;

        case ReEnquiryAction.TriggerWorkflow:
          // Currently not implemented, stay in current level
          this.logger.log(
            `TriggerWorkflow action not implemented yet for level ${currentLevelId}`,
          );
          return currentLevelId;

        default:
          // Default behavior is to stay in current level
          return currentLevelId;
      }
    } catch (error) {
      this.logger.error(
        `Error handling re-enquiry level transition for level ${currentLevelId}: ${error.message}`,
        error.stack,
      );
      // In case of error, return the current level ID as fallback
      return currentLevelId;
    }
  }

  async getLevelsByClientId(clientId: number): Promise<PaginatedResponse<any>> {
    try {
      // Get all lead levels for this client with related program information
      const leadLevels = await this.leadLevelRepository.find({
        where: { client_id: clientId },
        order: { program_id: 'ASC', id: 'ASC' },
        relations: ['program'], // Assuming program relation exists
        select: [
          'id',
          'name',
          'description',
          'display_order',
          'program_id',
          'client_id',
          'is_positive',
          'temperature',
          'enrollment_stage',
          'default_fud_duration_unit',
          'default_fud_duration_value',
          'max_fud_duration_unit',
          'max_fud_duration_value',
        ],
      });

      // Group levels by program_id
      const groupedLevelsObj = leadLevels.reduce((acc, level) => {
        const programId = level.program_id;
        if (!acc[programId]) {
          acc[programId] = [];
        }

        acc[programId].push({
          id: level.id,
          name: level.name,
          display_order: level.display_order,
          is_positive: level.is_positive,
          temperature: level.temperature,
          enroll_stage: level.enrollment_stage,
          date_range: {
            default: {
              unit: level.default_fud_duration_unit,
              value: level.default_fud_duration_value,
            },
            max: {
              unit: level.max_fud_duration_unit,
              value: level.max_fud_duration_value,
            },
          },
          description: level.description,
        });

        return acc;
      }, {});

      // Convert to array of programs with levels
      const programsArray = Object.entries(groupedLevelsObj).map(
        ([programId, levels]) => {
          // Find first level with this program_id to get program details
          const levelWithProgram = leadLevels.find(
            (l) => l.program_id === parseInt(programId),
          );
          return {
            program_id: parseInt(programId),
            program_name: levelWithProgram?.program?.name || 'Unknown Program',
            client_id: clientId,
            levels,
          };
        },
      );

      return {
        data: programsArray,
        meta: {
          total: leadLevels.length,
          page: 1,
          size: leadLevels.length,
          totalPages: 1,
          hasNextPage: false,
          hasPreviousPage: false,
        },
      };
    } catch (error) {
      this.logger.error(
        `Error fetching lead levels for client ${clientId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Apply level changes and create history records
   */
  async applyLevelChangesAndCreateHistory(params: {
    validatedUpdates: Array<{
      programInterest: LeadProgramInterest;
      newLevelId?: number;
      comments?: string;
    }>;
    dispositionHistoryId?: number;
    spocUserId: number;
    transitionMethod?: AllowedTransitionMethodEnum;
  }): Promise<void> {
    const {
      validatedUpdates,
      dispositionHistoryId,
      spocUserId,
      transitionMethod = AllowedTransitionMethodEnum.Manual,
    } = params;

    // Collect all level changes to create a single history entry
    const levels: Array<{
      next: string;
      initial: string;
      type: string;
      program: string;
      comment: string;
    }> = [];
    let leadId: number;
    let contactId: number;
    for (const update of validatedUpdates) {
      const { programInterest, newLevelId, comments } = update;

      // Update program interest comments
      if (comments) {
        await this.leadProgramInterestRepo.update(programInterest.id, {
          comments,
          updated_by: spocUserId,
        });
      }

      // If level is changing, update it and create history
      if (newLevelId && newLevelId !== programInterest.lead_level_id) {
        const oldLevelId = programInterest.lead_level_id;

        // Reload the entity without relations to ensure it's properly managed
        const managedProgramInterest =
          await this.leadProgramInterestRepo.findOne({
            where: { id: programInterest.id },
          });

        if (managedProgramInterest) {
          // Update the level on the managed entity
          managedProgramInterest.lead_level_id = newLevelId;
          managedProgramInterest.updated_by = spocUserId;

          //? Save to trigger automations/hooks
          //!! DO NOT USE any other update method here
          await this.leadProgramInterestRepo.save(managedProgramInterest);
          //!! DO NOT USE any other update method here
        }

        // Create level change history
        const levelHistory = this.leadLevelHistoryRepository.create({
          client_id: programInterest.client_id,
          lead_program_interest_id: programInterest.id,
          from_level_id: oldLevelId,
          to_level_id: newLevelId,
          changed_by_user_id: spocUserId,
          change_timestamp: new Date(),
          transition_method: transitionMethod,
          disposition_history_id: dispositionHistoryId || null,
          comments: comments || '',
        });
        await this.leadLevelHistoryRepository.save(levelHistory);
      }
      // Collect level change info for the single lead history
      levels.push({
        next: (
          await this.leadLevelRepository.findOne({
            where: { id: newLevelId },
          })
        ).name,
        initial: programInterest.lead_level?.name,
        type: programInterest.lead_level?.temperature,
        program: programInterest?.program?.name,
        comment: comments,
      });
      // Store lead and contact IDs for the history entry
      leadId = programInterest?.lead_id;
      contactId = (
        await this.leadRepository.findOne({
          where: { id: leadId },
          select: ['contact_id'],
        })
      )?.contact_id;
    }
    // create lead history for level updation
    this.leadHistoryService.createLeadHistory({
      action: ActionType.ENGAGEMENT,
      subAction: EngagementSubActionType.LEVEL_UPDATION,
      leadId,
      contactId,
      performedByUser: spocUserId
        ? await this.userService.findOne(spocUserId)
        : null,
      performedBy: spocUserId || null,
      levels,
      details: {
        transitionMethod,
        disposition: dispositionHistoryId
          ? (
              await this.dispositionHistoryRepository.findOne({
                where: { id: dispositionHistoryId },
                relations: ['final_disposition_node'],
              })
            )?.final_disposition_node?.display_text
          : null,
      },
      metadata: LeadLevelHistory,
    });
  }

  async createLevelChangeHistory({
    programInterestId,
    fromLevelId,
    toLevelId,
    userId,
    transitionMethod = AllowedTransitionMethodEnum.Manual,
    dispositionHistoryId = null,
    comments = '',
  }: {
    programInterestId: number;
    fromLevelId: number;
    toLevelId: number;
    userId?: number;
    comments?: string;
    transitionMethod?: AllowedTransitionMethodEnum;
    dispositionHistoryId?: number | null;
  }): Promise<LeadLevelHistory> {
    try {
      const leadProgramInterest = await this.leadProgramInterestRepo.findOne({
        where: { id: programInterestId },
        relations: ['lead', 'lead.contact'],
      });

      if (!leadProgramInterest) {
        throw new NotFoundException(
          `Lead Program Interest with ID ${programInterestId} not found`,
        );
      }

      const levelHistory = this.leadLevelHistoryRepository.create({
        client_id: leadProgramInterest.client_id,
        lead_program_interest_id: programInterestId,
        from_level_id: fromLevelId,
        to_level_id: toLevelId,
        changed_by_user_id: userId,
        change_timestamp: new Date(),
        transition_method: transitionMethod,
        disposition_history_id: dispositionHistoryId,
        comments,
      });
      // create lead history for level updation
      this.leadHistoryService.createLeadHistory({
        action: ActionType.ENGAGEMENT,
        subAction: EngagementSubActionType.LEVEL_UPDATION,
        leadId: leadProgramInterest?.lead_id,
        contactId: leadProgramInterest?.lead?.contact_id,
        performedByUser: userId ? await this.userService.findOne(userId) : null,
        performedBy: userId || null,
        levels: [
          {
            next: levelHistory?.to_level?.name,
            initial: levelHistory?.from_level?.name,
            type: leadProgramInterest?.lead_level?.temperature,
            program: leadProgramInterest?.program?.name,
            comment: comments,
          },
        ],
        details: {
          transitionMethod,
          disposition: dispositionHistoryId
            ? (
                await this.dispositionHistoryRepository.findOne({
                  where: { id: dispositionHistoryId },
                  relations: ['final_disposition_node'],
                })
              )?.final_disposition_node?.display_text
            : null,
        },
        metadata: levelHistory,
      });
      return await this.leadLevelHistoryRepository.save(levelHistory);
    } catch (error) {
      this.logger.error(
        `Error creating level change history for program interest ${programInterestId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async updateLeadProgIntLevel({
    programInterestId,
    currentLevelId,
    newLevelId,
    nextFollowUpDate,
    userId,
    comments = '',
    transitionMethod = AllowedTransitionMethodEnum.Manual,
  }: {
    programInterestId: number;
    currentLevelId: number;
    newLevelId: number;
    nextFollowUpDate?: string | null;
    userId: number;
    comments?: string;
    transitionMethod?: AllowedTransitionMethodEnum;
  }): Promise<LeadProgramInterest> {
    try {
      const currentLevel = await this.leadLevelRepository.findOne({
        where: { id: currentLevelId },
      });

      if (!currentLevel) {
        throw new NotFoundException(
          `Lead level with ID ${currentLevel} not found`,
        );
      }

      const leadProgramInterest = await this.leadProgramInterestRepo.findOne({
        where: { id: programInterestId },
        relations: ['lead'],
      });
      if (!leadProgramInterest) {
        throw new NotFoundException(
          `Lead Program Interest with ID ${programInterestId} not found`,
        );
      }

      // Check if the new level is the same as the current level
      if (leadProgramInterest.lead_level_id !== currentLevelId) {
        throw new BadRequestException(
          `Lead Program Interest with ID ${programInterestId} is not at the current level ${currentLevelId}`,
        );
      }

      const isTransitionValid = await this.isTransitionValid(
        newLevelId,
        currentLevelId,
        transitionMethod,
      );

      if (!isTransitionValid) {
        throw new NotFoundException(
          `Lead level with ID ${newLevelId} is not allowed to transition from current level ${currentLevelId}`,
        );
      }

      const newLevel = await this.leadLevelRepository.findOne({
        where: { id: newLevelId },
      });
      if (!newLevel) {
        throw new NotFoundException(
          `Lead level with ID ${newLevelId} not found`,
        );
      }

      // Determine and validate next follow-up date
      const validatedNextFollowUpDate = getNextFollowUpDateByLevel(
        newLevel,
        nextFollowUpDate,
      );

      leadProgramInterest.lead_level_id = newLevelId;

      //!! DO NOT USE any other update method here
      const updatedLeadProgramInterest =
        await this.leadProgramInterestRepo.save(leadProgramInterest);
      //!! DO NOT USE any other update method here

      // Update the lead's next follow-up date if we have a valid date
      if (leadProgramInterest.lead && validatedNextFollowUpDate) {
        await this.leadRepository.update(
          { id: leadProgramInterest.lead.id },
          { next_followup_date: validatedNextFollowUpDate },
        );
      }

      // Create level change history if level is changing
      if (newLevelId !== currentLevelId) {
        await this.applyLevelChangesAndCreateHistory({
          validatedUpdates: [
            {
              programInterest: leadProgramInterest,
              newLevelId,
              comments,
            },
          ],
          spocUserId: userId,
          transitionMethod,
          dispositionHistoryId: null,
        });
      }

      return updatedLeadProgramInterest;
    } catch (error) {
      this.logger.error(
        `Error updating lead level with ID ${programInterestId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  async isTransitionValid(
    intendedToLevelId: number,
    currentLevelId: number,
    transitionMethod: AllowedTransitionMethodEnum,
  ): Promise<boolean> {
    try {
      if (intendedToLevelId === currentLevelId) {
        return true;
      }

      const targetLevel = await this.leadLevelRepository.findOne({
        where: { id: intendedToLevelId },
      });
      const currentLevel = await this.leadLevelRepository.findOne({
        where: { id: currentLevelId },
      });

      if (!targetLevel || !currentLevel) {
        this.logger.warn(
          `Target level with ID ${intendedToLevelId} or current level with ID ${currentLevelId} not found`,
        );
        return false;
      }

      const isDowngrade =
        targetLevel.display_order < currentLevel.display_order;

      if (isDowngrade) {
        const allowedDowngradeLevels =
          currentLevel.allowed_downgrade_level_ids_json || [];

        if (
          allowedDowngradeLevels?.length === 0 ||
          !allowedDowngradeLevels.includes(intendedToLevelId) ||
          !currentLevel.allowed_transition_methods?.includes(transitionMethod)
        ) {
          return false;
        }
      } else {
        const currentEnrollmentStage = currentLevel.enrollment_stage;
        const targetEnrollmentStage = targetLevel.enrollment_stage;
        if (
          currentEnrollmentStage === EnrollmentStage.PreEnroll &&
          targetEnrollmentStage === EnrollmentStage.PostEnroll
        ) {
          return false;
        }
      }

      return true;
    } catch (error) {
      this.logger.error(
        `Error validating transition: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }

  async getHistoryByDisposition(
    dispositionId: number,
  ): Promise<LeadLevelHistory[]> {
    return await this.leadLevelHistoryRepository.find({
      where: { disposition_history_id: dispositionId },
      relations: [
        'from_level',
        'to_level',
        'lead_program_interest',
        'lead_program_interest.program',
      ],
    });
  }
}
