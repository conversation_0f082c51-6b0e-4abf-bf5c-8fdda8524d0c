import {
  ActionType,
  LeadManagementSubActionType,
} from '@modules/lead-histories/enums/lead-history.enum';
import { LeadHistoryService } from '@modules/lead-histories/services/lead-history.service';
import { UserService } from '@modules/users/services/user.service';
import { BadRequestException, Injectable } from '@nestjs/common';

@Injectable()
export class LeadsHistoryService {
  constructor(
    private readonly leadHistoryService: LeadHistoryService,
    private readonly userService: UserService,
  ) {}

  async createLeadDndHistory(details: any, userId: number) {
    try {
      await this.leadHistoryService.createLeadHistory({
        action: ActionType.LEAD_MANAGEMENT,
        subAction: LeadManagementSubActionType.LEAD_UPDATE,
        leadId: details?.leadId ?? null,
        contactId: details?.contactId ?? null,
        performedByUser: userId ? await this.userService.findOne(userId) : null,
        details: {
          actionType: details.actionType,
          previousStatus: details?.previousStatus ?? null,
          newStatus: details?.newStatus ?? null,
        },
        performedBy: userId ?? null,
      });
    } catch (error) {
      throw new BadRequestException(
        'Error creating lead DND history',
        error.message,
      );
    }
  }
}
