import {
  Injectable,
  NotFoundException,
  BadRequestException,
  ConflictException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { In, Repository } from 'typeorm';
import { Lead } from '../entities/lead.entity';
import {
  CreateLeadDto,
  createLeadWithRelationsDto,
} from '../dto/create-lead.dto';
import { UpdateLeadDto } from '../dto/update-lead.dto';
import { LeadProgramInterestService } from './lead-program-interest.service';
import { ContactService } from '../../contacts/services/contact.service';
import { Contact } from '../../contacts/entities/contact.entity';
import { CallStatus } from '@modules/call-logs/enums/call-log.enum';
import { LeadCallStatus } from '../enums/lead-status.enum';
import { GetLeadsQueryDto } from '../dto/get-leads-query.dto';
import { LeadHistoryService } from '@modules/lead-histories/services/lead-history.service';
import { UserService } from '@modules/users/services/user.service';
import { SpocType } from '../enums/spoc.enum';
import { LeadSpoc } from '../entities/lead-spoc.entity';
import { LeadAllocationHistoryService } from '@modules/lead-allocations/services/lead-allocation-history.service';
import { AllocationTypeEnum } from '@modules/lead-allocations/enums/allocation-type.enum';
import { Program } from '@modules/programs/entities/program.entity';
import { Logger } from '@nestjs/common';
import { User } from '@modules/users/entities/user.entity';
import { LeadSpocService } from './lead-spoc.service';
import {
  ActionType,
  LeadManagementSubActionType,
} from '@modules/lead-histories/enums/lead-history.enum';
import { Phone } from '@modules/contacts/entities/phone.entity';
import { CallLogService } from '@modules/call-logs/services/call-log.service';
import { LeadSessionIds } from '@modules/session/enums/lead-session-status.enum';
import { LeadTransformationService } from './lead-transformation.service';
import { NetEnquiry } from '@modules/net-enquiries/entities/net-enquiry.entity';
import { CampaignService } from '@modules/campaigns/services/campaign.service';
import { LeadLevelService } from './lead-level.service';
import axios from 'axios';
import { EnrollmentStatus } from '@modules/enrollments/types/enrollment-status.type';
import { PaginatedResWithEnrollmentMeta } from 'src/common/interfaces/paginationWithEnrollmentMeta.interface';

@Injectable()
export class LeadService {
  private readonly logger = new Logger(LeadService.name);
  constructor(
    @InjectRepository(Lead)
    private readonly leadRepository: Repository<Lead>,
    @InjectRepository(Program)
    private readonly programRepository: Repository<Program>,
    @InjectRepository(NetEnquiry)
    private readonly netEnquiryRepository: Repository<NetEnquiry>,
    @InjectRepository(Phone)
    private readonly phoneRepository: Repository<Phone>,
    private readonly leadProgramInterestService: LeadProgramInterestService,
    private readonly contactService: ContactService,
    private readonly leadHistoryService: LeadHistoryService,
    private readonly userService: UserService,
    private readonly leadAllocationHistoryService: LeadAllocationHistoryService,
    private readonly leadSpocService: LeadSpocService,
    private readonly callLogService: CallLogService,
    private readonly leadTransformationService: LeadTransformationService,
    private readonly campaignService: CampaignService,
    private readonly leadLevelService: LeadLevelService,
  ) {}

  /**
   * Handles the lead creation process based on contact information
   *
   * @param createLeadDto The lead information including contact details
   * @param userId The ID of the user making the request
   * @returns Information about the lead creation: the lead, whether it's new, and if it's a re-inquiry
   */
  async handleLead(
    createLeadDto: CreateLeadDto,
    userId: number,
  ): Promise<{ lead: Lead; isNew: boolean }> {
    this.logger.log('Starting handleLead process');
    this.logger.log('Received createLeadDto:', createLeadDto);

    console.log('handle lead createLeadDto', createLeadDto);
    console.log('user id in handle lead', userId);

    try {
      this.logger.log('Handling contact creation/update');
      const { contact } = await this.contactService.handleContact(
        createLeadDto.contact_info,
        userId,
      );
      this.logger.log(`Contact handled successfully with ID: ${contact.id}`);

      this.logger.log(
        `Checking for existing lead with client_id: ${createLeadDto.client_id} and contact_id: ${contact.id}`,
      );
      const existingLead = await this.findByClientAndContact(
        createLeadDto.client_id,
        contact.id,
      );

      if (existingLead) {
        this.logger.log(`Existing lead found with ID: ${existingLead.id}`);
        return {
          lead: existingLead,
          isNew: false,
        };
      }

      this.logger.log('No existing lead found, creating a new lead');
      const lead = await this.createLeadForContact(
        createLeadDto,
        contact,
        userId,
      );
      this.logger.log(`New lead created with ID: ${lead.id}`);

      return {
        lead,
        isNew: true,
      };
    } catch (error) {
      this.logger.error('Error in handleLead:', error);
      throw new Error('Failed to handle lead creation');
    }
  }

  /**
   * Creates a new lead for the given contact
   */
  private async createLeadForContact(
    createLeadDto: CreateLeadDto,
    contact: Contact,
    userId: number,
  ): Promise<Lead> {
    const { ...leadData } = createLeadDto;

    // Create a new lead for this contact in this client
    const lead = this.leadRepository.create({
      ...leadData,
      contact,
      contact_id: contact.id,
      created_by: userId,
      updated_by: userId,
    });

    const savedLead = await this.leadRepository.save(lead);

    const leadWithRelations = await this.findOne(savedLead.id);
    // Create lead history for lead creation
    this.createLeadCreationHistory(leadWithRelations, userId);
    return leadWithRelations;
  }

  async getAllLeads(
    queryParams: GetLeadsQueryDto,
    clientId: number,
    user: User,
  ): Promise<PaginatedResWithEnrollmentMeta<any>> {
    const {
      page = 1,
      size = 10,
      pattern: rawPattern,
      city_ids,
      program_ids,
      lead_created_start_date,
      lead_created_end_date,
      next_followup_start_date,
      next_followup_end_date,
      last_call_start_date,
      last_call_end_date,
      last_connected_call_start_date,
      last_connected_call_end_date,
      spoc_ids,
      campaign_ids,
      level_ids,
      past_webinar_status,
      upcoming_webinar_status,
      s1_counselling_status,
      s2_counselling_status,
      interview_status,
      university_ids,
      enrollment_status,
      enrollment_start_date,
      enrollment_end_date,
    } = queryParams;

    const pattern = rawPattern ? rawPattern.trim() : null;
    const skip = (page - 1) * size;

    const hasAdminAccess = await this.userService.isUserAdminOrSuperAdmin(
      user.id,
      clientId,
    );

    let leadSpocIds: number[] = [];
    if (!hasAdminAccess || spoc_ids?.length > 0) {
      const userIds = await this.callLogService.getUserIdsForFilter(
        spoc_ids,
        user,
      );
      const leadSpocs = await this.leadSpocService.findByUserIds(userIds);
      leadSpocIds = leadSpocs.map((spoc) => spoc.id);
    }

    // Build the base query with essential relations
    const queryBuilder = this.leadRepository
      .createQueryBuilder('lead')
      .leftJoinAndSelect('lead.contact', 'contact')
      .leftJoinAndSelect('contact.miles_office', 'milesOffice')
      .leftJoinAndSelect('contact.emails', 'emails')
      .leftJoinAndSelect('contact.phones', 'phones')
      .leftJoinAndSelect('contact.primary_email', 'primary_email')
      .leftJoinAndSelect('contact.primary_phone', 'primary_phone')
      .leftJoinAndSelect('lead.program_interests', 'programInterests')
      .leftJoinAndSelect('programInterests.program', 'program')
      .leftJoinAndSelect('programInterests.net_enquiries', 'netEnquiries')
      .leftJoinAndSelect('netEnquiries.campaign', 'netEnquiryCampaign')
      .leftJoinAndSelect(
        'programInterests.webinar_registrations',
        'webinarRegistrations',
      )
      .leftJoinAndSelect('webinarRegistrations.webinar', 'webinar')
      .leftJoinAndSelect('programInterests.lead_level', 'leadLevel')
      .leftJoinAndSelect('programInterests.lead_source', 'leadSource')
      .leftJoinAndSelect('programInterests.interviews', 'interviews')
      .leftJoinAndSelect('interviews.university', 'university')
      .leftJoinAndSelect('interviews.assigned_user', 'assignedUser')
      .leftJoinAndSelect('lead.owner_spoc', 'leadSpoc')
      .leftJoinAndSelect('lead.lead_sessions', 'leadSessions')
      .leftJoinAndSelect('leadSessions.session', 'sessionConfig')
      .leftJoinAndSelect('leadSpoc.spoc', 'spocUser')
      .where('lead.client_id = :clientId', { clientId });

    // Only add user filtering for non-admin users
    if (!hasAdminAccess || leadSpocIds.length > 0) {
      queryBuilder.andWhere('lead.owner_spoc_id IN (:...leadSpocIds)', {
        leadSpocIds,
      });
    }

    // Filter by enrollment status
    if (enrollment_status) {
      if (enrollment_status === EnrollmentStatus.ENROLLED) {
        queryBuilder.andWhere(
          `EXISTS (
          SELECT 1 
          FROM enrollment e 
          INNER JOIN lead_program_interest lpi ON e.lead_program_interest_id = lpi.id
          WHERE lpi.lead_id = lead.id
        )`,
        );
      } else if (enrollment_status === EnrollmentStatus.NOT_ENROLLED) {
        queryBuilder.andWhere(
          `NOT EXISTS (
          SELECT 1 
          FROM enrollment e 
          INNER JOIN lead_program_interest lpi ON e.lead_program_interest_id = lpi.id
          WHERE lpi.lead_id = lead.id
        )`,
        );
      }
    }

    // Filter by enrollment date range
    if (enrollment_start_date && enrollment_end_date) {
      queryBuilder.andWhere(
        `EXISTS (
        SELECT 1 
        FROM enrollment e 
        INNER JOIN lead_program_interest lpi ON e.lead_program_interest_id = lpi.id
        WHERE lpi.lead_id = lead.id
        AND e.enrollment_date BETWEEN :enrollmentStartDate AND :enrollmentEndDate
      )`,
        {
          enrollmentStartDate: new Date(enrollment_start_date),
          enrollmentEndDate: new Date(enrollment_end_date),
        },
      );
    } else if (enrollment_start_date) {
      queryBuilder.andWhere(
        `EXISTS (
        SELECT 1 
        FROM enrollment e 
        INNER JOIN lead_program_interest lpi ON e.lead_program_interest_id = lpi.id
        WHERE lpi.lead_id = lead.id
        AND e.enrollment_date >= :enrollmentStartDate
      )`,
        {
          enrollmentStartDate: new Date(enrollment_start_date),
        },
      );
    } else if (enrollment_end_date) {
      queryBuilder.andWhere(
        `EXISTS (
        SELECT 1 
        FROM enrollment e 
        INNER JOIN lead_program_interest lpi ON e.lead_program_interest_id = lpi.id
        WHERE lpi.lead_id = lead.id
        AND e.enrollment_date <= :enrollmentEndDate
      )`,
        {
          enrollmentEndDate: new Date(enrollment_end_date),
        },
      );
    }

    // Filter by campaign_ids if provided
    if (campaign_ids && campaign_ids.length > 0) {
      queryBuilder.andWhere(
        `EXISTS (
          SELECT 1 
          FROM (
            SELECT ne.campaign_id,
                   ROW_NUMBER() OVER (PARTITION BY lpi.lead_id ORDER BY ne.created_at ASC) as rn
            FROM net_enquiry ne
            INNER JOIN lead_program_interest lpi ON ne.lead_program_interest_id = lpi.id
            WHERE lpi.lead_id = lead.id
              AND ne.campaign_id IS NOT NULL
          ) first_enquiry
          WHERE first_enquiry.rn = 1 
            AND first_enquiry.campaign_id IN (:...campaignIds)
        )`,
        {
          campaignIds: campaign_ids,
        },
      );
    }

    // Filter by level_ids if provided
    if (level_ids && level_ids.length > 0) {
      queryBuilder.andWhere(
        'programInterests.lead_level_id IN (:...levelIds)',
        {
          levelIds: level_ids,
        },
      );
    }

    // Filter by pattern (search by email or candidate_id)
    if (pattern) {
      // Since we're already joining emails table above, we don't need to join it again
      queryBuilder.andWhere(
        '(contact.candidate_id ILIKE :pattern OR emails.email ILIKE :pattern)',
        { pattern: `%${pattern}%` },
      );
    }

    // Filter by city_ids
    if (city_ids && city_ids.length > 0) {
      queryBuilder.andWhere('contact.miles_office_id IN (:...cityIds)', {
        cityIds: city_ids,
      });
    }

    // Filter by program_ids
    if (program_ids && program_ids.length > 0) {
      queryBuilder.andWhere('programInterests.program_id IN (:...programIds)', {
        programIds: program_ids,
      });
    }

    // Filter by date range
    if (lead_created_start_date && lead_created_end_date) {
      queryBuilder.andWhere('lead.created_at BETWEEN :startDate AND :endDate', {
        startDate: new Date(lead_created_start_date),
        endDate: new Date(lead_created_end_date),
      });
    }

    // Filter by last call date range
    if (last_call_start_date && last_call_end_date) {
      queryBuilder.andWhere(
        'lead.last_call_date BETWEEN :lastCallStartDate AND :lastCallEndDate',
        {
          lastCallStartDate: new Date(last_call_start_date),
          lastCallEndDate: new Date(last_call_end_date),
        },
      );
    }

    // Filter by next followup date range
    if (next_followup_start_date && next_followup_end_date) {
      queryBuilder.andWhere(
        'lead.next_followup_date BETWEEN :followupStartDate AND :followupEndDate',
        {
          followupStartDate: new Date(next_followup_start_date),
          followupEndDate: new Date(next_followup_end_date),
        },
      );
    }

    // Filter by last connected call date range
    if (last_connected_call_start_date && last_connected_call_end_date) {
      // Use QueryBuilder with proper entity and column references
      const callLogSubQuery = this.leadRepository.manager
        .createQueryBuilder()
        .select('1')
        .from('call_log', 'cl')
        .where('cl.leadId = lead.id')
        .andWhere('cl.call_status = :attendedStatus')
        .andWhere(
          'cl.created_at BETWEEN :lastConnectedStartDate AND :lastConnectedEndDate',
        );

      queryBuilder.andWhere(`EXISTS (${callLogSubQuery.getQuery()})`, {
        attendedStatus: CallStatus.ATTENDED,
        lastConnectedStartDate: new Date(last_connected_call_start_date),
        lastConnectedEndDate: new Date(last_connected_call_end_date),
      });
    }

    // Filter by S1 counselling status
    if (s1_counselling_status) {
      queryBuilder.andWhere(
        `EXISTS (
          SELECT 1 
          FROM lead_session ls1 
          WHERE ls1.lead_id = lead.id 
          AND ls1.session_config_id = :sessionId 
          AND ls1.complete_status = :s1Status
        )`,
        {
          s1Status: s1_counselling_status,
          sessionId: LeadSessionIds.S1_COUNSELING,
        },
      );
    }

    // Filter by S2 counselling status
    if (s2_counselling_status) {
      queryBuilder.andWhere(
        `EXISTS (
          SELECT 1 
          FROM lead_session ls2 
          WHERE ls2.lead_id = lead.id 
          AND ls2.session_config_id = :s2SessionId 
          AND ls2.complete_status = :s2Status
        )`,
        {
          s2Status: s2_counselling_status,
          s2SessionId: LeadSessionIds.S2_COUNSELING,
        },
      );
    }

    // Filter by interview status
    if (interview_status) {
      queryBuilder.andWhere('interviews.interview_status = :interviewStatus', {
        interviewStatus: interview_status,
      });
    }

    // Filter by university IDs
    if (university_ids && university_ids.length > 0) {
      queryBuilder.andWhere('interviews.university_id IN (:...universityIds)', {
        universityIds: university_ids,
      });
    }

    // Filter by past webinar status (webinars that have already occurred)
    if (past_webinar_status) {
      const currentDate = new Date();
      queryBuilder.andWhere(
        `EXISTS (
          SELECT 1 
          FROM webinar_registration wr 
          INNER JOIN webinar w ON wr."webinarId" = w.id 
          INNER JOIN lead_program_interest lpi ON wr."leadProgramInterestId" = lpi.id
          WHERE lpi.lead_id = lead.id 
          AND w.date_time < :pastCurrentDate 
          AND wr.webinar_status = :pastWebinarStatus
        )`,
        {
          pastCurrentDate: currentDate,
          pastWebinarStatus: past_webinar_status,
        },
      );
    }

    // Filter by upcoming webinar status (webinars that are yet to occur)
    if (upcoming_webinar_status) {
      const currentDate = new Date();
      queryBuilder.andWhere(
        `EXISTS (
          SELECT 1 
          FROM webinar_registration wr 
          INNER JOIN webinar w ON wr."webinarId" = w.id 
          INNER JOIN lead_program_interest lpi ON wr."leadProgramInterestId" = lpi.id
          WHERE lpi.lead_id = lead.id 
          AND w.date_time >= :upcomingCurrentDate 
          AND wr.webinar_status = :upcomingWebinarStatus
        )`,
        {
          upcomingCurrentDate: currentDate,
          upcomingWebinarStatus: upcoming_webinar_status,
        },
      );
    }

    // Get total count for pagination
    const total = await queryBuilder.getCount();

    // Add pagination
    queryBuilder.skip(skip).take(size);

    // Add ordering by created_at desc to get newest leads first
    queryBuilder.orderBy('lead.created_at', 'DESC');

    // Execute query
    const leads = await queryBuilder.getMany();

    // Get lead IDs for batch fetching call dates
    const leadIds = leads.map((lead) => lead.id);

    // Batch fetch last connected call dates using CallLogService
    const lastConnectedCallDates =
      await this.callLogService.getLastConnectedCallDatesForLeads(leadIds);

    // Transform the data using the transformation service (EXACT SAME LOGIC, just moved)
    const transformedData = leads.map((lead) =>
      this.leadTransformationService.transformLeadForResponse(
        lead,
        lastConnectedCallDates,
      ),
    );

    // Calculate pagination metadata
    const totalPages = Math.ceil(total / size);

    return {
      data: transformedData,
      meta: {
        total,
        page,
        size,
        totalPages,
        hasNextPage: page < totalPages,
        hasPreviousPage: page > 1,
        enrollment_status: enrollment_status ? enrollment_status : null,
      },
    };
  }

  async createLeadWithRelations(
    dto: createLeadWithRelationsDto,
    userId: number,
    clientId: number,
  ): Promise<Lead> {
    try {
      const {
        contact_info,
        program_id,
        level_id,
        next_followup_date,
        phone_id,
        call_log_id,
        ...leadData
      } = dto;

      // ----------------------------------- Validate the inputs -----------------------------------
      if (!userId) {
        throw new BadRequestException('User ID is required');
      }

      if (!program_id) {
        throw new BadRequestException('Program ID is required');
      }

      if (!level_id) {
        throw new BadRequestException('Lead level ID is required');
      }

      if (!contact_info) {
        throw new BadRequestException('Contact information is required');
      }

      if (!contact_info.first_name || !contact_info.last_name) {
        throw new BadRequestException('First name and last name are required');
      }

      if (!contact_info.phone) {
        throw new BadRequestException('Phone number is required');
      }

      const phoneNumber = contact_info.phone.phone_number;
      const cleanPhone = phoneNumber.replace(/\D/g, '');
      if (!cleanPhone) {
        if (!phone_id) {
          throw new BadRequestException(
            'Either phone number or phone ID is required',
          );
        }
        const phone = await this.phoneRepository.findOne({
          where: { id: phone_id },
        });
        if (!phone) {
          throw new NotFoundException(`Phone with ID ${phone_id} not found`);
        }
        if (phone.contact_id) {
          throw new BadRequestException(
            'Phone ID is already associated with another contact',
          );
        }
        contact_info.phone = {
          phone_number: phone.phone_number,
          country_code: phone.country_code,
        };
      }

      const payloadToGetCanId = {
        first_name: contact_info.first_name || '',
        last_name: contact_info.last_name || '',
        miles_office_id: contact_info.miles_office_id || null,
        email: contact_info.email?.email || '',
        phone: contact_info.phone?.phone_number || '',
        country_code: contact_info.phone?.country_code || '',
        program_id: program_id || null,
      };

      let candidateId = contact_info.candidate_id || null;
      try {
        const MF2URL = process.env.MF2_URL;

        const result = await axios.post(
          `${MF2URL}/leads/create-lead-from-mfpro`,
          payloadToGetCanId,
        );
        const data = result.data;

        // Fetch the CAN ID from the external service
        candidateId = data?.result?.Candidate_id;
      } catch (error) {
        this.logger.error('Error while fetching CAN ID:', error);
        throw new BadRequestException(
          'Unable to generate candidate ID. Please try again or contact support.',
        );
      }

      // ----------------------------------- Validate the inputs -----------------------------------
      if (!candidateId) {
        throw new BadRequestException(
          'Candidate ID could not be generated. Please check the provided information.',
        );
      }

      if (!clientId) {
        throw new BadRequestException(
          'Client context is missing. Please refresh and try again.',
        );
      }

      let campaign = null;
      if (dto.campaign_id) {
        try {
          // Fetch the campaign to ensure it exists
          campaign = await this.campaignService.findOne(dto.campaign_id);
          if (!campaign) {
            throw new NotFoundException(
              `Campaign with ID ${dto.campaign_id} not found`,
            );
          }
        } catch (error) {
          if (error instanceof NotFoundException) {
            throw error;
          }
          this.logger.error('Error validating campaign:', error);
          throw new BadRequestException(
            'Unable to validate campaign. Please check the campaign ID.',
          );
        }
      }

      // Check if the program belongs to the client
      let program;
      try {
        program = await this.programRepository.findOne({
          where: { id: program_id, client_id: clientId },
        });

        if (!program) {
          throw new NotFoundException(
            `Program with ID ${program_id} not found or does not belong to your organization`,
          );
        }
      } catch (error) {
        if (error instanceof NotFoundException) {
          throw error;
        }
        this.logger.error('Error validating program:', error);
        throw new BadRequestException(
          'Unable to validate program. Please check the program ID.',
        );
      }

      // Check if the level exists
      let level;
      try {
        level = await this.leadLevelService.findById(level_id);
        if (!level) {
          throw new NotFoundException(
            `Lead level with ID ${level_id} not found`,
          );
        }

        if (level.client_id !== clientId) {
          throw new BadRequestException(
            'Lead level does not belong to your organization',
          );
        }

        if (level.program_id !== program_id) {
          throw new BadRequestException(
            'Lead level does not belong to the specified program',
          );
        }
      } catch (error) {
        if (
          error instanceof NotFoundException ||
          error instanceof BadRequestException
        ) {
          throw error;
        }
        this.logger.error('Error validating lead level:', error);
        throw new BadRequestException(
          'Unable to validate lead level. Please check the level ID.',
        );
      }

      // ----------------------------------- Create entities -----------------------------------

      // Create contact
      let contact;
      try {
        const contactResult = await this.contactService.handleContact(
          { ...contact_info, candidate_id: candidateId },
          userId,
        );
        contact = contactResult.contact;
      } catch (error) {
        this.logger.error('Error creating contact:', error);
        throw new BadRequestException(
          'Unable to create contact. Please check the contact information and try again.',
        );
      }

      //check if lead exist
      let leadDetails = await this.findByClientAndContact(clientId, contact.id);
      let createNewLeadProgramInterest = true;
      if (leadDetails) {
        //check lead program interest
        const existingLeadProgramInterest =
          await this.leadProgramInterestService.findByLeadIdAndProgramId(
            leadDetails.id,
            program_id,
          );

        if (existingLeadProgramInterest) {
          createNewLeadProgramInterest = false;

          this.logger.warn(
            `Lead with contact ID ${contact.id} already exists for program ID ${program_id}`,
          );
          throw new ConflictException(
            `Lead with contact ID ${contact.id} already exists for program ID ${program_id}`,
          );
        }
      } else {
        try {
          const lead = this.leadRepository.create({
            ...leadData,
            client_id: clientId,
            contact,
            contact_id: contact.id,
            created_by: userId,
            updated_by: userId,
            next_followup_date: next_followup_date
              ? new Date(next_followup_date)
              : null,
          });

          leadDetails = await this.leadRepository.save(lead);
        } catch (error) {
          this.logger.error('Error creating lead:', error);
          throw new BadRequestException(
            'Unable to create lead. Please check the lead information and try again.',
          );
        }
      }

      // Create lead program interest
      let leadProgramInterest;
      if (createNewLeadProgramInterest) {
        try {
          leadProgramInterest = await this.leadProgramInterestService.create(
            {
              lead_id: leadDetails.id,
              client_id: clientId,
              program_id,
              lead_level_id: level_id,
              is_primary: true,
            },
            leadDetails.id,
            userId,
          );
        } catch (error) {
          this.logger.error('Error creating lead program interest:', error);
          throw new BadRequestException(
            'Lead created but unable to associate with program. Please contact support.',
          );
        }
      }

      // Add a net enquiry here
      try {
        const netEnquiry = this.netEnquiryRepository.create({
          lead_program_interest: leadProgramInterest,
          lead_program_interest_id: leadProgramInterest.id,
          program: program,
          coming_from: 'Direct Lead Creation',
          campaign: campaign,
        });

        await this.netEnquiryRepository.save(netEnquiry);
      } catch (error) {
        this.logger.error('Error creating net enquiry:', error);
        throw new BadRequestException(
          'Lead created but unable to create enquiry record. Please contact support.',
        );
      }

      // Add the current user as a CC SPOC for this lead
      let savedLeadSpoc;
      try {
        const leadSpocDto = {
          lead_id: leadDetails.id,
          spoc_id: userId,
          spoc_type: SpocType.CC,
          client_id: clientId,
          is_initial_owner: true,
        };

        // Create the LeadSpoc
        const leadSpoc = this.leadRepository.manager.create(
          LeadSpoc,
          leadSpocDto,
        );
        savedLeadSpoc = await this.leadRepository.manager.save(leadSpoc);
      } catch (error) {
        this.logger.error('Error creating lead SPOC:', error);
        throw new BadRequestException(
          'Lead created but unable to assign ownership. Please contact support.',
        );
      }

      // Update the lead's owner_spoc reference to point to this SPOC
      try {
        await this.leadRepository.update(leadDetails.id, {
          owner_spoc_id: savedLeadSpoc.id,
          updated_at: new Date(),
        });
      } catch (error) {
        this.logger.error('Error updating lead ownership:', error);
        throw new BadRequestException(
          'Lead created but unable to update ownership. Please contact support.',
        );
      }

      // Record the allocation history
      try {
        await this.leadAllocationHistoryService.recordAllocationHistoryNonTransactional(
          {
            lead: leadDetails,
            spocId: userId,
            allocationType: AllocationTypeEnum.MANUAL_REASSIGNMENT,
            allocatedBy: `user_${userId}`,
            notes: 'Initial lead creation with default CC SPOC',
            clientId: clientId,
            cityId: contact.miles_office_id,
            allocatedByUserId: userId,
          },
        );
      } catch (error) {
        this.logger.error('Error recording allocation history:', error);
        // Don't throw error here as it's not critical for lead creation
      }

      //finally attach the lead to the call log
      if (call_log_id) {
        try {
          await this.callLogService.assignleadToUntrackedCalls(
            leadDetails,
            call_log_id,
          );
        } catch (error) {
          this.logger.error('Error attaching lead to call log:', error);
          throw new BadRequestException(
            'Lead created but unable to attach to call log. Please contact support.',
          );
        }
      }

      // Get final lead with relations and create history
      try {
        const leadWithRelations = await this.findOne(leadDetails.id);
        this.createLeadCreationHistory(leadWithRelations, userId);

        // Return the lead with all relations
        return leadWithRelations;
      } catch (error) {
        this.logger.error('Error fetching created lead:', error);
        throw new BadRequestException(
          'Lead created successfully but unable to fetch complete details. Please refresh to see the lead.',
        );
      }
    } catch (error) {
      // If it's already a known HTTP exception, re-throw it
      if (
        error instanceof BadRequestException ||
        error instanceof ConflictException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }

      // For any other unexpected errors
      this.logger.error('Unexpected error in createLeadWithRelations:', error);
      throw new BadRequestException(
        'An unexpected error occurred while creating the lead. Please try again or contact support.',
      );
    }
  }

  /**
   * Searches for leads globally across all clients using an exact pattern
   * that matches candidate ID, email, or phone number (case insensitive)
   *
   * @param pattern Search pattern for exact candidate ID, email or phone
   * @returns Response with matching lead or empty with message
   */
  async findGlobalLeads(
    pattern: string,
    clientId: number,
  ): Promise<{
    data: any | null;
    message?: string;
  }> {
    try {
      // Clean up the pattern
      const trimmedPattern = pattern ? pattern.trim() : '';

      // If no pattern provided, return empty result
      if (!trimmedPattern) {
        return {
          data: null,
          message: 'No search pattern provided',
        };
      }
      if (!clientId) {
        throw new NotFoundException(`client id is required`);
      }

      // Create a more optimized query that reduces the number of joins
      // and only fetches what we need initially
      const queryBuilder = this.leadRepository
        .createQueryBuilder('lead')
        .leftJoinAndSelect('lead.contact', 'contact')
        .leftJoinAndSelect('contact.emails', 'emails')
        .leftJoinAndSelect('contact.phones', 'phones')
        .where('lead.client_id = :clientId', { clientId })
        .andWhere(
          '(LOWER(contact.candidate_id) = LOWER(:pattern) OR LOWER(emails.email) = LOWER(:pattern) OR phones.phone_number = :pattern)',
          { pattern: trimmedPattern },
        )
        .orderBy('lead.created_at', 'DESC');

      // Execute query to find just the lead ID
      const result = await queryBuilder.getOne();

      // If no lead found, return empty with message
      if (!result) {
        return {
          data: null,
          message: 'No lead found matching the provided pattern',
        };
      }

      // Now that we have a match, fetch the complete data with all relations
      // This is more efficient as we only do the heavy join for one record
      const lead = await this.leadRepository.findOne({
        where: { id: result.id },
        relations: [
          'owner_spoc',
          'owner_spoc.spoc',
          'owner_spoc.spoc.manager',
          'spocs',
          'contact',
          'contact.miles_office',
          'contact.emails',
          'contact.phones',
          'program_interests',
          'program_interests.program',
          'client',
        ],
      });

      // Mask sensitive data before returning
      if (lead?.contact) {
        if (lead.contact.emails) {
          lead.contact.emails.forEach((email) => {
            delete email.email; // Remove the masked_email field
          });
        }

        // Mask phones - only return masked_phone_number, remove actual phone
        if (lead.contact.phones) {
          lead.contact.phones.forEach((phone) => {
            delete phone.phone_number; // Remove the masked_phone_number field
          });
        }
      }

      // Return the data with full details
      return {
        data: lead,
      };
    } catch (error) {
      console.error('Error in findGlobalLeads:', error);
      return {
        data: null,
        message: 'An error occurred while searching for leads',
      };
    }
  }

  async findOne(id: number): Promise<Lead> {
    const lead = await this.leadRepository.findOne({
      where: { id },
      relations: [
        'contact',
        'contact.primary_email',
        'contact.primary_phone',
        'contact.miles_office',
        'program_interests',
        'owner_spoc',
        'owner_spoc.spoc',
        'owner_spoc.spoc.manager',
        'program_interests.program',
        'program_interests.lead_source',
      ],
    });

    if (!lead) {
      throw new NotFoundException(`Lead with ID ${id} not found`);
    }

    return lead;
  }

  async findByClientAndContact(
    clientId: number,
    contactId: number,
  ): Promise<Lead | null> {
    const lead = await this.leadRepository.findOne({
      where: {
        client_id: clientId,
        contact_id: contactId,
      },
      relations: ['contact', 'contact.primary_email', 'contact.primary_phone'],
    });

    return lead; // May be null if not found
  }

  /**
   * Finds a lead by searching through phone numbers
   * @param phone Phone entity to search with
   * @param clientId Client context ID
   */
  async findLeadByPhone(phone: Phone, clientId: number): Promise<Lead | null> {
    try {
      const lead = await this.leadRepository
        .createQueryBuilder('lead')
        .innerJoinAndSelect('lead.contact', 'contact')
        .innerJoinAndSelect('contact.phones', 'phone')
        .where('phone.phone_number = :phoneNumber', {
          phoneNumber: phone.phone_number,
        })
        .andWhere('lead.client_id = :clientId', { clientId })
        .getOne();

      return lead || null;
    } catch (error) {
      Logger.error('Find lead by phone failed:', error);
      return null;
    }
  }

  /**
   * Find all leads for a contact across all business units
   */
  async findByContactId(contactId: number): Promise<Lead[]> {
    this.logger.debug(`Finding all leads for contact ID: ${contactId}`);

    try {
      const leads = await this.leadRepository.find({
        where: { contact_id: contactId },
        relations: [
          'contact',
          'program_interests',
          'owner_spoc',
          'owner_spoc.spoc',
        ],
      });

      this.logger.debug(
        `Found ${leads.length} leads for contact ID: ${contactId}`,
      );
      return leads;
    } catch (error) {
      this.logger.error(
        `Error finding leads for contact ID ${contactId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Find leads for a contact within specific business units (clients)
   */
  async findByContactIdAndClients(
    contactId: number,
    clientIds: number[],
  ): Promise<Lead[]> {
    this.logger.debug(
      `Finding leads for contact ID: ${contactId} within clients: ${clientIds.join(', ')}`,
    );

    if (clientIds.length === 0) {
      this.logger.debug('No client IDs provided, returning empty array');
      return [];
    }

    try {
      const leads = await this.leadRepository.find({
        where: {
          contact_id: contactId,
          client_id: In(clientIds),
        },
        relations: [
          'contact',
          'program_interests',
          'owner_spoc',
          'owner_spoc.spoc',
        ],
      });

      this.logger.debug(
        `Found ${leads.length} leads for contact ID: ${contactId} within specified clients`,
      );
      return leads;
    } catch (error) {
      this.logger.error(
        `Error finding leads for contact ID ${contactId} within clients: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Check if a contact has leads in specific business units
   */
  async hasLeadsInClients(
    contactId: number,
    clientIds: number[],
  ): Promise<boolean> {
    if (clientIds.length === 0) {
      return false;
    }

    try {
      const count = await this.leadRepository.count({
        where: {
          contact_id: contactId,
          client_id: In(clientIds),
        },
      });

      return count > 0;
    } catch (error) {
      this.logger.error(
        `Error checking leads for contact ID ${contactId}: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }

  async update(
    id: number,
    updateLeadDto: UpdateLeadDto,
    userId: number,
  ): Promise<Lead> {
    const lead = await this.findOne(id);

    // Update the lead
    const updatedLead = {
      ...lead,
      ...updateLeadDto,
      updated_by: userId,
    };

    await this.leadRepository.save(updatedLead);

    return this.findOne(id);
  }

  async markAsConverted(id: number, userId: number): Promise<Lead> {
    const lead = await this.findOne(id);
    // lead.is_converted = true;
    lead.updated_by = userId;

    return this.leadRepository.save(lead);
  }

  async setPrimaryProgram(
    leadId: number,
    programId: number,
    userId: number,
  ): Promise<void> {
    await this.leadProgramInterestService.setPrimaryProgram(
      leadId,
      programId,
      userId,
    );
  }
  /**
   * Updates a lead's last call date and status after a call is completed
   * @param lead_id The ID of the lead to update
   * @param call_status The status of the call (ATTENDED or MISSED)
   * @param date The date/time of the call
   * @param isMobile Whether the call was made from a mobile device
   */
  async updateLastCallDate(
    lead_id: number,
    call_status: CallStatus,
    date: Date,
  ): Promise<Lead> {
    try {
      // Find the lead by ID
      const lead = await this.leadRepository.findOne({
        where: { id: lead_id },
      });

      if (!lead) {
        return null;
      }

      // Update the last call date
      lead.last_call_date = new Date(date);

      // Update call status based on attendance status
      lead.lead_call_status =
        call_status === CallStatus.ATTENDED
          ? LeadCallStatus.CALLED
          : lead.lead_call_status === LeadCallStatus.MHP
            ? LeadCallStatus.MHP
            : LeadCallStatus.CALLED;

      // Handle MHP logic for missed calls with MHP status
      if (lead.lead_call_status === LeadCallStatus.MHP) {
        lead.attempt_count = (lead.attempt_count || 0) + 1;

        const currentDate = new Date();
        const differenceInDays = lead.next_followup_date
          ? Math.floor(
              (currentDate.getTime() - lead.next_followup_date.getTime()) /
                (1000 * 60 * 60 * 24),
            )
          : 0;

        // Change status to CALLED if both conditions are met
        if (differenceInDays >= 3 && lead.attempt_count >= 3) {
          lead.lead_call_status = LeadCallStatus.CALLED;
        }
      }

      // Save and return the updated lead
      return await this.leadRepository.save(lead);
    } catch (error) {
      throw error;
    }
  }

  /**
   * Creates a lead history entry for lead creation
   * @param leadWithRelations The lead with its relations loaded
   * @param userId The ID of the user who performed the action
   */
  private async createLeadCreationHistory(
    leadWithRelations: Lead,
    userId: number,
  ): Promise<void> {
    await this.leadHistoryService.createLeadHistory({
      action: ActionType.LEAD_MANAGEMENT,
      subAction: LeadManagementSubActionType.LEAD_CREATED,
      leadId: leadWithRelations?.id,
      contactId: leadWithRelations?.contact?.id,
      performedByUser: userId ? await this.userService.findOne(userId) : null,
      details: {
        candidateId: leadWithRelations?.contact?.candidate_id,
        candidateName: leadWithRelations?.contact?.full_name,
        source: leadWithRelations?.program_interests?.[0]?.lead_source?.name,
        city: leadWithRelations?.contact?.miles_office?.city,
      },
      performedBy: userId ?? null,
    });
  }
}
