import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  HttpCode,
  HttpStatus,
  Req,
  UseGuards,
  Query,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiBody,
  ApiQuery,
} from '@nestjs/swagger';
import { createLeadWithRelationsDto } from '../dto/create-lead.dto';
import { UpdateLeadDto } from '../dto/update-lead.dto';
import { Lead } from '../entities/lead.entity';
import { LeadService } from '../services/lead.service';
import { AuthenticatedRequest } from '@modules/auth/interfaces/request.interface';
import { ApiResource } from '@modules/auth/decorators/resource.decorator';
import { PermissionGuard } from '@modules/auth/guards/permission.guard';
import { Resource, Action } from '@modules/permissions/enums/permission.enum';
import { RequirePermission } from '@modules/auth/decorators/permission.decorator';
import { GetLeadsQueryDto } from '../dto/get-leads-query.dto';
import { BaseDto } from 'src/common/dto/base.dto';
import { UUID } from 'crypto';
import { LeadDetailService } from '../services/lead-detail.service';
import { PaginatedResWithEnrollmentMeta } from 'src/common/interfaces/paginationWithEnrollmentMeta.interface';
import { GetGlobalLeadDto } from '../dto/get-global-lead.dto';
import { Public } from '@modules/auth/decorators/public.decorator';

@ApiTags('leads')
@UseGuards(PermissionGuard)
@ApiResource(Resource.LEAD)
@Controller('leads')
export class LeadController {
  constructor(
    private readonly leadService: LeadService,
    private readonly leadDetailService: LeadDetailService,
  ) {}

  @Get('global')
  @RequirePermission({ resource: Resource.LEAD, action: Action.READ })
  async findGlobalLeads(
    @Query() query: BaseDto,
    @Req() req: AuthenticatedRequest,
  ): Promise<{ data: Lead }> {
    const clientId = req.user?.currentClientId;
    return this.leadService.findGlobalLeads(query.pattern, clientId);
  }

  @Public()
  @Get('public-global')
  @RequirePermission({ resource: Resource.LEAD, action: Action.READ })
  async findPublicGlobalLeads(
    @Query() query: GetGlobalLeadDto,
    @Req() req: AuthenticatedRequest,
  ): Promise<{ data: Lead }> {
    const clientId = req.user?.currentClientId || query.client_id;
    return this.leadService.findGlobalLeads(query.pattern, +clientId);
  }

  // Lead Details
  @Get('detail/:uuid')
  @RequirePermission({ resource: Resource.LEAD, action: Action.READ })
  @ApiOperation({ summary: 'Get lead by UUID' })
  @ApiParam({ name: 'uuid', description: 'Lead UUID' })
  async findLeadDetails(@Param('uuid') uuid: UUID) {
    return this.leadDetailService.findLeadDetailByUUID(uuid);
  }

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @RequirePermission({ resource: Resource.LEAD, action: Action.CREATE })
  @ApiOperation({ summary: 'Create a new lead' })
  @ApiBody({ type: createLeadWithRelationsDto })
  async createWithRelations(
    @Body() createLeadDto: createLeadWithRelationsDto,
    @Req() req: AuthenticatedRequest,
  ): Promise<Lead> {
    const userId = req.user?.id || null;
    const clientId = req.user?.currentClientId;
    const lead = await this.leadService.createLeadWithRelations(
      createLeadDto,
      userId,
      createLeadDto.client_id || clientId,
    );
    return lead;
  }

  @Get()
  @RequirePermission({ resource: Resource.LEAD, action: Action.READ })
  @ApiOperation({
    summary: 'Get all leads with optional filtering and pagination',
  })
  @ApiQuery({ type: GetLeadsQueryDto })
  async getAllLeads(
    @Query() queryParams: GetLeadsQueryDto,
    @Req() req: AuthenticatedRequest,
  ): Promise<PaginatedResWithEnrollmentMeta<any>> {
    const clientId = req.user?.currentClientId;
    const user = req.user;
    return this.leadService.getAllLeads(queryParams, clientId, user);
  }

  @Get(':id')
  @RequirePermission({ resource: Resource.LEAD, action: Action.READ })
  @ApiOperation({ summary: 'Get lead by ID' })
  @ApiParam({ name: 'id', description: 'Lead ID' })
  async findOne(@Param('id') id: string): Promise<Lead> {
    return this.leadService.findOne(+id);
  }

  @Patch(':id')
  @RequirePermission({ resource: Resource.LEAD, action: Action.UPDATE })
  @ApiOperation({ summary: 'Update lead details' })
  @ApiParam({ name: 'id', description: 'Lead ID' })
  @ApiBody({ type: UpdateLeadDto })
  async update(
    @Param('id') id: string,
    @Body() updateLeadDto: UpdateLeadDto,
    @Req() req: AuthenticatedRequest,
  ): Promise<Lead> {
    const userId = req.user?.id || null;
    return this.leadService.update(+id, updateLeadDto, userId);
  }

  @Patch(':id/convert')
  @RequirePermission({ resource: Resource.LEAD, action: Action.UPDATE })
  @ApiOperation({ summary: 'Mark lead as converted' })
  @ApiParam({ name: 'id', description: 'Lead ID' })
  async markAsConverted(
    @Param('id') id: string,
    @Req() req: AuthenticatedRequest,
  ): Promise<Lead> {
    const userId = req.user?.id || null;
    return this.leadService.markAsConverted(+id, userId);
  }
}
