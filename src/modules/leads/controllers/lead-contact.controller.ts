import {
  Controller,
  Body,
  Patch,
  Req,
  UseGuards,
  Get,
  Param,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiBody,
  ApiParam,
  ApiResponse,
} from '@nestjs/swagger';
import { Lead } from '../entities/lead.entity';
import { AuthenticatedRequest } from '@modules/auth/interfaces/request.interface';
import { ApiResource } from '@modules/auth/decorators/resource.decorator';
import { PermissionGuard } from '@modules/auth/guards/permission.guard';
import { Resource, Action } from '@modules/permissions/enums/permission.enum';
import { RequirePermission } from '@modules/auth/decorators/permission.decorator';
import { LeadContactService } from '../services/lead-contact.service';
import { UpdateLeadMetaDto, UpdateNameDto } from '../dto/update-lead.dto';
import { HandleUpdatePhoneTypeDto } from '../dto/update-lead-phone-type.dto';

@ApiTags('leads/contact')
@UseGuards(PermissionGuard)
@ApiResource(Resource.LEAD)
@Controller('leads/contact')
export class LeadContactController {
  constructor(private readonly leadContactService: LeadContactService) {}

  @Patch('metadata')
  @RequirePermission({ resource: Resource.LEAD, action: Action.UPDATE })
  @ApiOperation({ summary: 'Update lead metadata' })
  @ApiBody({ type: UpdateLeadMetaDto })
  async updateMetadata(
    @Body() data: UpdateLeadMetaDto,
    @Req() req: AuthenticatedRequest,
  ): Promise<{
    data: Lead;
    success: boolean;
  }> {
    const userId = req.user?.id || null;
    return this.leadContactService.updateContactMetaDetails(
      data.leadId,
      userId,
      data.metaDetails,
    );
  }

  @Patch('name')
  @RequirePermission({ resource: Resource.LEAD, action: Action.UPDATE })
  @ApiOperation({ summary: 'Update lead contact name' })
  @ApiBody({ type: UpdateNameDto })
  async updateContactName(
    @Body() updateNameDto: UpdateNameDto,
    @Req() req: AuthenticatedRequest,
  ): Promise<Lead> {
    const userId = req.user?.id || null;
    return this.leadContactService.handleContactNameUpdate(
      updateNameDto.leadId,
      userId,
      updateNameDto.firstName,
      updateNameDto.lastName,
    );
  }

  @Get('email/:emailId')
  @RequirePermission({ resource: Resource.CONTACT, action: Action.READ })
  @ApiOperation({
    summary: 'View email details by ID',
    description:
      'Retrieves detailed information about a specific email including its associated contact',
  })
  @ApiParam({
    name: 'emailId',
    type: 'number',
    description: 'The ID of the email to retrieve',
  })
  @ApiResponse({
    status: 200,
    description: 'Email details retrieved successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Email not found',
  })
  viewEmail(
    @Param('emailId') emailId: string,
    @Req() req: AuthenticatedRequest,
  ) {
    const userId = req.user?.id ?? null;
    const clientId = req.user?.currentClientId ?? null;
    return this.leadContactService.viewEmail(+emailId, userId, clientId);
  }

  @Get('phone/:phoneId')
  @RequirePermission({ resource: Resource.CONTACT, action: Action.READ })
  @ApiOperation({
    summary: 'View phone details by ID',
    description:
      'Retrieves detailed information about a specific phone number including its associated contact',
  })
  @ApiParam({
    name: 'phoneId',
    type: 'number',
    description: 'The ID of the phone to retrieve',
  })
  @ApiResponse({
    status: 200,
    description: 'Phone details retrieved successfully',
  })
  @ApiResponse({
    status: 404,
    description: 'Phone not found',
  })
  viewPhone(
    @Param('phoneId') phoneId: string,
    @Req() req: AuthenticatedRequest,
  ) {
    const userId = req?.user?.id ?? null;
    const clientId = req?.user?.currentClientId ?? null;
    return this.leadContactService.viewPhone(+phoneId, userId, clientId);
  }

  @Patch('add-phone')
  @RequirePermission({ resource: Resource.CONTACT, action: Action.UPDATE })
  @ApiOperation({ summary: 'Add phone to lead contact' })
  async addPhoneToLeadContact(
    @Body() body: { phone_id: number; lead_id: number; call_log_id?: number },
    @Req() req: AuthenticatedRequest,
  ): Promise<Lead> {
    const userId = req.user?.id || null;
    return this.leadContactService.addPhoneToLeadContact(
      body.phone_id,
      body.lead_id,
      body.call_log_id,
      userId,
    );
  }

  @Patch('phone-lead/attributes')
  @ApiOperation({
    summary: 'Updates phone types for Phone and dnd for leads',
  })
  async updatePhoneAndLeadAttributes(
    @Body() body: HandleUpdatePhoneTypeDto,
    @Req() req: AuthenticatedRequest,
  ): Promise<{ success: boolean; message: string }> {
    const userId = req.user.id;
    return this.leadContactService.handleUpdatePhoneType(body, userId);
  }
}
