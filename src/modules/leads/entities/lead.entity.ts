import {
  <PERSON><PERSON><PERSON>,
  Column,
  OneToMany,
  ManyToOne,
  Join<PERSON><PERSON>umn,
  Index,
  OneToOne,
  JoinTable,
  Unique,
} from 'typeorm';
import { Contact } from 'src/modules/contacts/entities/contact.entity';
import { ClientAwareEntity } from 'src/common/entities/client-aware.entity';
import { LeadProgramInterest } from './lead-program-interest.entity';
import { LeadSpoc } from './lead-spoc.entity';
import { LeadAllocationHistory } from '@modules/lead-allocations/entities';
import { LeadCallStatus } from '../enums/lead-status.enum';
import { LeadSession } from '@modules/session/entities/lead-session.entity';
import { UUID } from 'crypto';

@Entity()
@Unique('UQ_lead_contact_client', ['contact_id', 'client_id'])
export class Lead extends ClientAwareEntity {
  @Index()
  @ManyToOne(() => Contact, { nullable: true })
  @JoinColumn({ name: 'contact_id' })
  contact: Contact;

  @Column({ type: 'integer', nullable: true })
  contact_id: number;

  @Column({ type: 'uuid', default: () => 'gen_random_uuid()', unique: true })
  lead_uuid: UUID;

  @Column({ type: 'text', nullable: true })
  notes: string;

  @OneToOne(() => LeadSpoc, (leadSpoc) => leadSpoc.owner_relation)
  @JoinColumn({ name: 'owner_spoc_id' })
  owner_spoc: LeadSpoc;

  @Column({ type: 'integer', nullable: true })
  owner_spoc_id: number;

  @Column({ type: 'timestamp with time zone', nullable: true })
  next_followup_date: Date;

  @Column({ type: 'boolean', nullable: true, default: false })
  is_DND: boolean;

  @Index()
  @Column({
    type: 'timestamp with time zone',
    nullable: true,
  })
  last_call_date: Date;

  @OneToMany(
    () => LeadProgramInterest,
    (leadProgramInterest) => leadProgramInterest.lead,
  )
  program_interests: LeadProgramInterest[];

  @Column({ type: 'integer', nullable: true })
  parent_lead_id: number;

  @ManyToOne(() => Lead, (lead) => lead.child_leads)
  @JoinColumn({ name: 'parent_lead_id' })
  parent_lead: Lead;

  @OneToMany(() => Lead, (lead) => lead.parent_lead)
  child_leads: Lead[];

  @Column({ type: 'integer', nullable: true })
  attempt_count: number;

  @OneToMany(() => LeadSpoc, (leadSpoc) => leadSpoc.lead)
  spocs: LeadSpoc[];

  @Column({
    type: 'enum',
    enum: LeadCallStatus,
    default: LeadCallStatus.NOTCALLED,
    nullable: true,
  })
  lead_call_status: LeadCallStatus;

  @OneToMany(
    () => LeadAllocationHistory,
    (leadAllocation) => leadAllocation.lead,
    {
      cascade: true,
    },
  )
  lead_allocations: LeadAllocationHistory[];

  @OneToMany(() => LeadSession, (leadSession) => leadSession.lead)
  @JoinTable({ name: 'lead_session' })
  lead_sessions: LeadSession[];
}
