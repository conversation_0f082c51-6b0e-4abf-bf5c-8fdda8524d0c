import { Body, Controller, Patch, Post, Req, UseGuards } from '@nestjs/common';
import { ApiResource } from '@modules/auth/decorators/resource.decorator';
import { PermissionGuard } from '@modules/auth/guards/permission.guard';
import { Action, Resource } from '@modules/permissions/enums/permission.enum';
import { ClientAccessGuard } from '@modules/user-clients/guards/client-access.guard';
import { ContactService } from './services/contact.service';
import {
  AddContactInfoDto,
  UpdateContactPhoneDto,
  UpdatePrimaryDetailsDto,
} from './dto/update-contact.dto';
import { RequirePermission } from '@modules/auth/decorators/permission.decorator';
import { AuthenticatedRequest } from '@modules/auth/interfaces/request.interface';
import { ApiTags } from '@nestjs/swagger';

@ApiTags('contacts')
@Controller('contacts')
@UseGuards(ClientAccessGuard)
@ApiResource(Resource.CONTACT)
@UseGuards(PermissionGuard)
export class ContactController {
  constructor(private readonly contactService: ContactService) {}

  @Patch('phone-type')
  @RequirePermission({ resource: Resource.CONTACT, action: Action.UPDATE })
  async updatePhoneType(
    @Body() phoneTypeData: UpdateContactPhoneDto,
    @Req() req: AuthenticatedRequest,
  ) {
    return this.contactService.updatePhoneType(
      { ...phoneTypeData },
      req.user?.id,
    );
  }

  @Patch('primary-details')
  @RequirePermission({ resource: Resource.CONTACT, action: Action.UPDATE })
  async updatePrimaryDetails(
    @Body() primaryDetailsData: UpdatePrimaryDetailsDto,
  ) {
    return this.contactService.updatePrimaryDetails({
      ...primaryDetailsData,
    });
  }

  @Post('contact-info')
  @RequirePermission({ resource: Resource.CONTACT, action: Action.UPDATE })
  async updateContactInfo(
    @Body() contactInfoData: AddContactInfoDto,
    @Req() req: AuthenticatedRequest,
  ) {
    const userId = req.user?.id;
    return this.contactService.addContactInfo(contactInfoData, userId);
  }

  // @Get('email/:emailId')
  // @RequirePermission({ resource: Resource.CONTACT, action: Action.READ })
  // @ApiOperation({
  //   summary: 'View email details by ID',
  //   description:
  //     'Retrieves detailed information about a specific email including its associated contact',
  // })
  // @ApiParam({
  //   name: 'emailId',
  //   type: 'number',
  //   description: 'The ID of the email to retrieve',
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Email details retrieved successfully',
  // })
  // @ApiResponse({
  //   status: 404,
  //   description: 'Email not found',
  // })
  // async viewEmail(
  //   @Param('emailId') emailId: string,
  //   @Req() req: AuthenticatedRequest,
  // ) {
  //   const userId = req.user?.id;
  //   // return this.contactService.viewEmail(+emailId, userId);
  // }

  // @Get('phone/:phoneId')
  // @RequirePermission({ resource: Resource.CONTACT, action: Action.READ })
  // @ApiOperation({
  //   summary: 'View phone details by ID',
  //   description:
  //     'Retrieves detailed information about a specific phone number including its associated contact',
  // })
  // @ApiParam({
  //   name: 'phoneId',
  //   type: 'number',
  //   description: 'The ID of the phone to retrieve',
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Phone details retrieved successfully',
  // })
  // @ApiResponse({
  //   status: 404,
  //   description: 'Phone not found',
  // })
  // async viewPhone(
  //   @Param('phoneId') phoneId: string,
  //   @Req() req: AuthenticatedRequest,
  // ) {
  //   const userId = req.user?.id;
  //   // return this.contactService.viewPhone(+phoneId, userId);
  // }

  // @Get(':id')
  // findOne(@Param('id') id: string) {
  //   return this.contactService.findOne(+id);
  // }

  // @Patch(':id')
  // update(@Param('id') id: string, @Body() updateContactDto: UpdateContactDto) {
  //   return this.contactService.updateContact(+id, updateContactDto, userId);
  // }

  // @Delete(':id')
  // @HttpCode(HttpStatus.NO_CONTENT)
  // remove(@Param('id') id: string) {
  //   return this.contactService.remove(+id);
  // }
}
