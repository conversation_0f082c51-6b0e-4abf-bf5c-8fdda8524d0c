import { PartialType } from '@nestjs/mapped-types';
import { CreateContactDto } from './create-contact.dto';
import { PhoneType } from '../enums/contact.enum';
import { IsEnum, IsNumber, IsString, IsOptional } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class UpdateContactDto extends PartialType(CreateContactDto) {
  // Optional ID field to explicitly specify the contact to update
  id?: number;
}

export class UpdateContactPhoneDto {
  @ApiProperty({
    description: 'ID of the phone to update',
    example: 1,
  })
  @IsNumber()
  phoneId: number;

  @ApiProperty({
    enum: PhoneType,
    description: 'Type of phone number',
  })
  @IsEnum(PhoneType)
  phoneType: PhoneType;
}

export class UpdatePrimaryDetailsDto {
  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  primaryEmailId?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  primaryPhoneId?: number;

  @ApiProperty()
  @IsNumber()
  contactId: number;
}

export class AddContactInfoDto {
  @ApiProperty()
  @IsNumber()
  contactId: number;

  @ApiPropertyOptional({
    description: 'Email to add to the contact',
    example: '<EMAIL>',
  })
  @IsOptional()
  @IsString()
  email?: string;

  @ApiPropertyOptional({
    description: 'Email label',
    example: 'Personal',
  })
  @IsOptional()
  @IsString()
  emailLabel?: string;

  @ApiPropertyOptional({
    description: 'Phone number to add to the contact',
    example: '9876543210',
  })
  @IsOptional()
  @IsString()
  phoneNumber?: string;

  @ApiPropertyOptional({
    description: 'Country code for the phone number',
    example: '+91',
  })
  @IsOptional()
  @IsString()
  countryCode?: string;

  @ApiPropertyOptional({
    description: 'Phone label',
    example: 'Mobile',
  })
  @IsOptional()
  @IsString()
  phoneLabel?: string;
}
