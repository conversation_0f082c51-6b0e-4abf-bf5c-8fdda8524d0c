import {
  IsBoolean,
  IsDate<PERSON>tring,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { UUID } from 'crypto';

export class CreateCampaignDto {
  @ApiProperty({ description: 'Name of the campaign' })
  @IsNotEmpty()
  @IsString()
  campaign_name: string;

  @IsNotEmpty()
  @IsNumber()
  @ApiProperty()
  source_id: number;

  @ApiPropertyOptional({
    description: 'Execution time of the campaign',
    type: String,
    format: 'date-time',
  })
  @IsOptional()
  @IsDateString()
  execution_time?: Date;

  @ApiPropertyOptional({ description: 'Unique UUID for the campaign' })
  @IsOptional()
  @IsUUID()
  uuid?: UUID;

  @ApiPropertyOptional({ description: 'Replay flow identifier' })
  @IsOptional()
  @IsString()
  replay_flow?: string;

  @ApiPropertyOptional({ description: 'Campaign ID on the platform' })
  @IsOptional()
  @IsString()
  platform_campaignId?: string;

  @ApiPropertyOptional({ description: 'Medium used in the campaign' })
  @IsOptional()
  @IsString()
  medium?: string;

  @ApiPropertyOptional({ description: 'Identifier for the campaign' })
  @IsOptional()
  @IsString()
  identifier?: string;

  @ApiPropertyOptional({ description: 'Person who requested the campaign' })
  @IsOptional()
  @IsString()
  requested_by?: string;

  @ApiPropertyOptional({ description: 'Objective of the campaign' })
  @IsOptional()
  @IsString()
  objective?: string;

  @ApiPropertyOptional({ description: 'Term associated with the campaign' })
  @IsOptional()
  @IsString()
  term?: string;

  @ApiPropertyOptional({ description: 'YouTube link related to the campaign' })
  @IsOptional()
  @IsString()
  youtube_link?: string;

  @ApiPropertyOptional({ description: 'Destination URL for campaign traffic' })
  @IsOptional()
  @IsString()
  destination_url?: string;

  @ApiPropertyOptional({ description: 'WhatsApp message content' })
  @IsOptional()
  @IsString()
  whatsapp_message?: string;

  @ApiPropertyOptional({
    description: 'Flag to indicate if lead should be added',
  })
  @IsOptional()
  @IsBoolean()
  is_to_add_lead?: boolean;

  @ApiPropertyOptional({
    description: 'Flag to indicate if this is a webinar campaign',
  })
  @IsOptional()
  @IsBoolean()
  is_webinar?: boolean;

  @ApiPropertyOptional({ description: 'Email of the campaign owner' })
  @IsOptional()
  @IsString()
  owner_email?: string;

  @ApiPropertyOptional({ description: 'UUID of the campaign owner' })
  @IsOptional()
  @IsUUID()
  owner_id?: string;

  @ApiPropertyOptional({ description: 'Flag to enable super agent' })
  @IsOptional()
  @IsBoolean()
  enable_super_agent?: boolean;
}
