import { UUID } from 'crypto';
import { ClientAwareEntity } from 'src/common/entities/client-aware.entity';
import { EmailTemplate } from '@modules/emails/entities/email-template.entity';
import { Entity, Column, Index, ManyToOne, JoinColumn } from 'typeorm';
import { LeadSource } from '@modules/lead-allocations/entities';

@Entity()
export class Campaign extends ClientAwareEntity {
  @Column({ type: 'varchar' })
  campaign_name: string;

  @Column({ type: 'int', nullable: true }) // make it nullable (optional)
  lead_source_id?: number;

  @ManyToOne(() => LeadSource, { nullable: true }) // also mark relation as optional
  @JoinColumn({ name: 'lead_source_id' })
  lead_source?: LeadSource;

  @Index()
  @ManyToOne(() => EmailTemplate)
  template: EmailTemplate;

  @Column({ type: 'timestamptz', nullable: true })
  execution_time: Date;

  @Column({ type: 'uuid', nullable: true, default: () => 'gen_random_uuid()' })
  uuid: UUID;

  @Column({ type: 'varchar', nullable: true })
  replay_flow: string;

  @Column({ type: 'varchar', nullable: true })
  platform_campaignId: string;

  @Column({ type: 'varchar', nullable: true })
  medium: string;

  @Column({ type: 'varchar', nullable: true })
  identifier: string;

  @Column({ type: 'varchar', nullable: true })
  requested_by: string;

  @Column({ type: 'varchar', nullable: true })
  objective: string;

  @Column({ type: 'varchar', nullable: true })
  term: string;

  @Column({ type: 'varchar', nullable: true })
  youtube_link: string;

  @Column({ type: 'varchar', nullable: true })
  destination_url: string;

  @Column({ type: 'varchar', nullable: true })
  whatsapp_message: string;

  @Column({ type: 'boolean', nullable: true, default: false })
  is_to_add_lead: boolean;

  @Column({ type: 'boolean', nullable: true, default: false })
  is_webinar: boolean;

  @Column({ type: 'varchar', nullable: true })
  owner_email: string;

  @Column({ type: 'uuid', nullable: true })
  owner_id: string;

  @Column({ type: 'boolean', nullable: true, default: false })
  enable_super_agent: boolean;
}
