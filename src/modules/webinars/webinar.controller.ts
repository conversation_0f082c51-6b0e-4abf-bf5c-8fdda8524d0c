import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  HttpCode,
  HttpStatus,
  Query,
  Req,
  Delete,
  HttpException,
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  Logger,
} from '@nestjs/common';
import { WebinarService } from './services/webinar.service';
import { RegisterLeadForWebinarsDto } from './dto/register-lead-for-webinars.dto';
import { Webinar } from './entities/webinar.entity';
import { WebinarRegistration } from './entities/webinar-registration.entity';
import { Request, Response } from 'express';
import {
  ApiTags,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';
import { CreateWebinarDto } from './dto/create-webinar.dto';
import { UpdateWebinarDto } from './dto/update-webinar.dto';
import { Public } from '@modules/auth/decorators/public.decorator';
import { ZOOM_CONFIGS, ZoomAccount } from './zoom';
import * as crypto from 'crypto';
import {
  MOCK_PARTICIPANT_JOINED_PAYLOAD,
  MOCK_PARTICIPANT_LEFT_PAYLOAD,
  MOCK_WEBINAR_ENDED_PAYLOAD,
} from './constants/webinar-payload.constants';
import { AuthenticatedRequest } from '@modules/auth/interfaces/request.interface';

@ApiTags('webinars')
@Controller('webinars')
export class WebinarController {
  constructor(private readonly webinarService: WebinarService) {}

  @Public()
  @Post('add')
  create(@Body() createWebinarDto: CreateWebinarDto, @Req() req: Request) {
    return this.webinarService.create(createWebinarDto, req.user);
  }

  @Get()
  async getAllWebinars(@Req() req: AuthenticatedRequest) {
    const clientId = req.user?.currentClientId;
    const webinars = await this.webinarService.findAllWebinars(clientId);
    return {
      data: webinars,
      meta: {
        total: webinars.length,
      },
    };
  }

  @Post('edit')
  update(@Body() updateWebinarDto: UpdateWebinarDto, @Req() req: Request) {
    return this.webinarService.update(updateWebinarDto, req.user);
  }

  @Delete('delete/:id')
  remove(@Param('id') id: number, @Req() req: Request) {
    return this.webinarService.remove(+id, req.user);
  }

  @Get('upcoming/:clientId')
  @ApiOperation({
    summary:
      'Get upcoming webinars for a client, optionally filtered by program',
  })
  @ApiParam({ name: 'clientId', description: 'Client ID' })
  @ApiQuery({
    name: 'programId',
    required: false,
    description: 'Program ID to filter webinars',
    type: Number,
  })
  async findUpcomingWebinars(
    @Param('clientId') clientId: string,
    @Query('programId') programId?: string,
  ): Promise<Webinar[]> {
    return this.webinarService.findAllUpcomingWebinars(
      +clientId,
      programId ? +programId : undefined,
    );
  }

  @Get('upcoming-auto-register/:clientId')
  @ApiOperation({
    summary:
      'Get upcoming webinars with auto-registration enabled for a client, optionally filtered by program',
  })
  @ApiParam({ name: 'clientId', description: 'Client ID' })
  @ApiQuery({
    name: 'programId',
    required: false,
    description: 'Program ID to filter webinars',
    type: Number,
  })
  async findUpcomingAutoRegisterWebinars(
    @Param('clientId') clientId: string,
    @Query('programId') programId?: string,
  ): Promise<Webinar[]> {
    return this.webinarService.findAllUpcomingAutoRegisterWebinars(
      +clientId,
      programId ? +programId : undefined,
    );
  }

  @Post('register-lead')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Register a lead for all upcoming webinars' })
  @ApiBody({ type: RegisterLeadForWebinarsDto })
  async registerLeadForAllWebinars(
    @Body() registerDto: RegisterLeadForWebinarsDto,
  ): Promise<{ registered: number; webinars: WebinarRegistration[] }> {
    // In a real app, you would get the user ID from the request context/JWT
    const userId = 1; // Placeholder - would come from auth system
    return this.webinarService.registerLeadForAllUpcomingWebinars(
      registerDto,
      userId,
    );
  }

  @Get('registrations/lead/:leadProgramInterestId/client/:clientId')
  @ApiOperation({
    summary: 'Get webinar registrations for a lead under a specific client',
  })
  @ApiParam({
    name: 'leadProgramInterestId',
    description: 'Lead Program Interest ID',
  })
  @ApiParam({ name: 'clientId', description: 'Client ID' })
  async getLeadWebinarRegistrations(
    @Param('leadProgramInterestId') leadProgramInterestId: string,
    @Param('clientId') clientId: string,
  ): Promise<WebinarRegistration[]> {
    return this.webinarService.findWebinarRegistrationsForLead(
      +leadProgramInterestId,
      +clientId,
    );
  }

  @Public()
  @Post('webinar-participant-joined')
  async testWebinarParticipantJoined(@Body() data: any, @Res() res: Response) {
    // Simulated payload of participant joined
    const mockParticipantJoinedPayload = MOCK_PARTICIPANT_JOINED_PAYLOAD;

    // Mocking the request body as if it were coming from Zoom
    Logger.log(
      `Received mocked Zoom callback payload for participant joined: ${JSON.stringify(
        mockParticipantJoinedPayload,
        null,
        2,
      )}`,
    );

    // Simulate calling your main handler
    const config =
      ZOOM_CONFIGS['zvrNCCaRTca0xHc7r9JEEA'] || ZoomAccount.ACCOUNT_7;

    console.log('Config for Zoom account:', config);

    const secretToken = config?.webhookToken;

    // Mock the event logic (like it would happen in your main handler)
    const { event, payload } = mockParticipantJoinedPayload;

    if (event === 'webinar.participant_joined') {
      const webinarId = payload?.object?.id;
      if (!webinarId) {
        return res.status(400).json({ error: 'Missing webinar ID in payload' });
      }

      const webinar = await this.webinarService.findByZoomId(webinarId);
      if (!webinar) {
        return res.status(404).json({ error: 'Webinar not found' });
      }

      console.log('webinar zoom account:', webinar.zoomAccount);

      const participantEmail = payload?.object?.participant?.email;
      await this.webinarService.handleWebinarJoin(
        participantEmail,
        webinarId,
        webinar.zoomAccount,
      );

      return res
        .status(200)
        .json({ message: 'Participant joined successfully' });
    }

    return res.status(400).json({ error: 'Invalid event type' });
  }

  @Public()
  @Post('webinar-participant-left')
  async testWebinarParticipantLeft(@Body() data: any, @Res() res: Response) {
    // Simulated payload of participant left
    const mockParticipantLeftPayload = MOCK_PARTICIPANT_LEFT_PAYLOAD;
    // Mocking the request body as if it were coming from Zoom
    Logger.log(
      `Received mocked Zoom callback payload for participant left: ${JSON.stringify(
        mockParticipantLeftPayload,
        null,
        2,
      )}`,
    );

    // Simulate calling your main handler
    const config =
      ZOOM_CONFIGS['zvrNCCaRTca0xHc7r9JEEA'] || ZoomAccount.ACCOUNT_7;

    console.log('Config for Zoom account:', config);

    const secretToken = config?.webhookToken;

    // Mock the event logic (like it would happen in your main handler)
    const { event, payload } = mockParticipantLeftPayload;

    if (event === 'webinar.participant_left') {
      const webinarId = payload?.object?.id;
      if (!webinarId) {
        return res.status(400).json({ error: 'Missing webinar ID in payload' });
      }

      const webinar = await this.webinarService.findByZoomId(webinarId);
      if (!webinar) {
        return res.status(404).json({ error: 'Webinar not found' });
      }

      console.log('webinar zoom account:', webinar.zoomAccount);

      const participantEmail = payload?.object?.participant?.email;
      await this.webinarService.handleWebinarLeft(participantEmail, webinarId);

      return res.status(200).json({ message: 'Participant left successfully' });
    }

    return res.status(400).json({ error: 'Invalid event type' });
  }

  @Public()
  @Post('webinar-ended')
  async testWebinarEnded(@Body() data: any, @Res() res: Response) {
    // Simulated payload of webinar ended
    const mockWebinarEndedPayload = MOCK_WEBINAR_ENDED_PAYLOAD;

    // Mocking the request body as if it were coming from Zoom
    Logger.log(
      `Received mocked Zoom callback payload for webinar ended: ${JSON.stringify(
        mockWebinarEndedPayload,
        null,
        2,
      )}`,
    );

    // Simulate calling your main handler
    const config =
      ZOOM_CONFIGS['zvrNCCaRTca0xHc7r9JEEA'] || ZoomAccount.ACCOUNT_7;

    console.log('Config for Zoom account:', config);

    const secretToken = config?.webhookToken;

    // Mock the event logic (like it would happen in your main handler)
    const { event, payload } = mockWebinarEndedPayload;

    if (event === 'webinar.ended') {
      const webinarId = payload?.object?.id;
      if (!webinarId) {
        return res.status(400).json({ error: 'Missing webinar ID in payload' });
      }

      const webinar = await this.webinarService.findByZoomId(webinarId);
      if (!webinar) {
        return res.status(404).json({ error: 'Webinar not found' });
      }

      console.log('Webinar zoom account:', webinar.zoomAccount);

      await this.webinarService.handleWebinarEnd(webinarId);

      return res.status(200).json({ message: 'Webinar ended successfully' });
    }

    return res.status(400).json({ error: 'Invalid event type' });
  }

  @Public()
  @Post('callback')
  async handleZoomCallback(
    @Body() data: any,
    @Headers('x-zm-request-timestamp') timestamp: string,
    @Headers('x-zm-signature') signature: string,
    @Headers('x-zm-account-id') accountId: string,
    @Res() res: Response,
  ): Promise<any> {
    console.log('Received Zoom callback data:', JSON.stringify(data, null, 2));

    const config = ZOOM_CONFIGS[accountId || ZoomAccount.ACCOUNT_7];

    Logger.log(
      `Received Zoom callback for account ${accountId} at ${new Date().toISOString()}`,
      'WebinarController',
    );

    const secretToken = config?.webhookToken;
    if (data.event === 'endpoint.url_validation') {
      const plainToken = data.payload.plainToken;

      const encryptedToken = crypto
        .createHmac('sha256', secretToken)
        .update(plainToken)
        .digest('hex');

      return res.status(200).json({
        plainToken,
        encryptedToken,
      });
    }

    // Get webinar ID from the payload
    const webinarId = data?.payload?.object?.id;

    if (!webinarId) {
      throw new HttpException(
        'Invalid payload: missing webinar ID',
        HttpStatus.BAD_REQUEST,
      );
    }

    // Find the webinar to determine which Zoom account it belongs to
    const webinar = await this.webinarService.findByZoomId(webinarId);
    if (!webinar) {
      throw new HttpException('Webinar not found', HttpStatus.NOT_FOUND);
    }

    if (data.event === 'webinar.participant_joined') {
      Logger.log(
        'Data payload in webinar.participant_joined event: ',
        data.payload,
      );
      const participantEmail = data?.payload?.object?.participant?.email;
      await this.webinarService.handleWebinarJoin(
        participantEmail,
        webinarId,
        webinar.zoomAccount,
      );
    } else if (data.event === 'webinar.participant_left') {
      Logger.log(
        'Data payload in webinar.participant_left event: ',
        data.payload,
      );
      const participantEmail = data?.payload?.object?.participant?.email;
      await this.webinarService.handleWebinarLeft(participantEmail, webinarId);
    } else if (data.event === 'webinar.ended') {
      Logger.log('Data payload in webinar.ended event: ', data.payload);
      const zoomWebinarId = data?.payload?.object?.id;
      await this.webinarService.handleWebinarEnd(zoomWebinarId);
    }

    return { message: 'Callback received successfully' };
  }
}
