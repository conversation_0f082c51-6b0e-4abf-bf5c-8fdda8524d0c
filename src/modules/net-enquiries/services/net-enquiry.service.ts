import {
  Injectable,
  NotFoundException,
  Logger,
  Inject,
  forwardRef,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { NetEnquiry, NEType } from '../entities/net-enquiry.entity';
import { CreateNetEnquiryDto } from '../dto/create-net-enquiry.dto';
import { UpdateNetEnquiryDto } from '../dto/update-net-enquiry.dto';
import { ProgramService } from 'src/modules/programs/services/program.service';
import { CampaignService } from 'src/modules/campaigns/services/campaign.service';
import { LeadProgramInterestService } from 'src/modules/leads/services/lead-program-interest.service';
import { CreateLeadDto } from 'src/modules/leads/dto/create-lead.dto';
import { CreateLeadProgramInterestDto } from 'src/modules/leads/dto/create-lead-program-interest.dto';
import { WebinarService } from 'src/modules/webinars/services/webinar.service';
import { LeadService } from 'src/modules/leads/services/lead.service';
import { LeadAllocationService } from 'src/modules/lead-allocations/services/lead-allocation.service';
import { LeadProgramInterest } from 'src/modules/leads/entities/lead-program-interest.entity';
import { LeadCallStatus } from '@modules/leads/enums/lead-status.enum';
import { locations } from 'src/common/constants/locations';
import { LeadLevelService } from 'src/modules/leads/services/lead-level.service';
import { LeadHistoryService } from 'src/modules/lead-histories/services/lead-history.service';
import {
  ActionType,
  EngagementSubActionType,
} from 'src/modules/lead-histories/enums/lead-history.enum';
import { UserService } from '@modules/users/services/user.service';
import { AllowedTransitionMethodEnum } from '@modules/leads/enums/lead-level.enum';
import { logToMongoDB } from '@modules/audit/general_log/utils/logger.util';
import { MongoLogType } from '@modules/audit/general_log/schemas/general_log.schema';
import { AllocationTypeEnum } from '@modules/lead-allocations/enums/allocation-type.enum';
import { LeadAllocationHistoryService } from '@modules/lead-allocations/services/lead-allocation-history.service';

@Injectable()
export class NetEnquiryService {
  private readonly logger = new Logger(NetEnquiryService.name);

  constructor(
    @InjectRepository(NetEnquiry)
    private readonly netEnquiryRepository: Repository<NetEnquiry>,
    private readonly programService: ProgramService,
    private readonly campaignService: CampaignService,
    private readonly leadProgramInterestService: LeadProgramInterestService,
    private readonly leadService: LeadService,
    @Inject(forwardRef(() => WebinarService))
    private readonly webinarService: WebinarService,
    private readonly leadAllocationService: LeadAllocationService,
    private readonly leadLevelService: LeadLevelService,
    private readonly leadHistoryService: LeadHistoryService,
    private readonly userService: UserService,
    private readonly leadAllocationHistoryService: LeadAllocationHistoryService,
  ) {}

  async create(
    createNetEnquiryDto: CreateNetEnquiryDto,
    userId?: number,
  ): Promise<NetEnquiry> {
    logToMongoDB(MongoLogType.EVENT, {
      data: createNetEnquiryDto,
      entityType: 'candidateId',
      entityId: createNetEnquiryDto?.contact_info?.candidate_id || 'N/A',
      source: 'Create Net Enquiry',
    });
    const { spoc_phone } = createNetEnquiryDto || {};

    this.logger.log('Starting create net enquiry process');
    const programId = createNetEnquiryDto.program_id;

    if (!programId) {
      this.logger.error('Program ID is missing in createNetEnquiryDto');
      throw new Error('Program ID is required');
    }

    this.logger.log('CREATE NET ENQUIRY DTO:', createNetEnquiryDto);

    let program;
    let netEnquiry;
    let campaign;
    let lead;
    let finalLeadProgInt: LeadProgramInterest | null = null;

    try {
      this.logger.log(`Fetching program with ID: ${programId}`);
      program = await this.programService.findOne(programId);

      if (!program) {
        this.logger.error(`Program with ID ${programId} not found`);
        throw new Error(`Program with ID ${programId} not found`);
      }

      this.logger.log('Program found:', program);

      netEnquiry = this.netEnquiryRepository.create({
        ...createNetEnquiryDto,
        created_by: userId,
      });
      netEnquiry.program = program;

      this.logger.log('Net enquiry object created:', netEnquiry);
    } catch (error) {
      this.logger.error(
        `Error initializing net enquiry: ${error.message}`,
        error.stack,
      );
      throw new Error('Failed to initialize net enquiry with program');
    }

    if (createNetEnquiryDto.campaign_id) {
      try {
        this.logger.log(
          `Fetching campaign with ID: ${createNetEnquiryDto.campaign_id}`,
        );
        campaign = await this.campaignService.findOne(
          createNetEnquiryDto.campaign_id,
        );
        netEnquiry.campaign = campaign;
        this.logger.log('Campaign found and set:', campaign);
      } catch (error) {
        this.logger.warn(
          `Campaign with ID ${createNetEnquiryDto.campaign_id} not found: ${error.message}`,
          error.stack,
        );
      }
    }

    this.logger.log('Processing location for milesOfficeId if applicable');

    let milesOfficeId = createNetEnquiryDto.miles_office_id || null;

    //Attach miles office based on num_verify_location and num_verify_country_code
    if (!milesOfficeId && createNetEnquiryDto.num_verify_location) {
      const numVerifyLocation = createNetEnquiryDto.num_verify_location.trim();
      const numVerifyCountryCode =
        createNetEnquiryDto.num_verify_country_code.trim();

      this.logger.log(
        `Finding office for country code ${numVerifyCountryCode} and location ${numVerifyLocation}`,
      );
      const offices = locations.filter((location) => {
        return (
          location.countryShort === numVerifyCountryCode &&
          (location.state === numVerifyLocation ||
            location.mappedStates.includes(numVerifyLocation) ||
            location.mappedCities.includes(numVerifyLocation))
        );
      });

      let office;
      if (offices.length === 0) {
        if (numVerifyCountryCode !== 'IN') {
          office = {
            country: 'UAE',
            countryShort: 'UAE',
            state: 'Dubai',
            city: 'Dubai',
            office_id: 10,
            mappedStates: [],
          };
          this.logger.log('Default UAE office assigned due to no matches');
        } else {
          this.logger.log('No matching office found for IN location');
        }
      } else {
        office = offices[0];
        this.logger.log('Office found:', office);
      }

      milesOfficeId = office?.office_id ?? null;
      this.logger.log(`Miles office ID set to: ${milesOfficeId}`);
    }

    const leadDto: CreateLeadDto = {
      contact_info: createNetEnquiryDto.contact_info
        ? {
            ...createNetEnquiryDto.contact_info,
            miles_office_id: milesOfficeId,
          }
        : {
            first_name: createNetEnquiryDto.first_name,
            last_name: createNetEnquiryDto.last_name,
            email: {
              email: createNetEnquiryDto.email,
            },
            phone: {
              phone_number: createNetEnquiryDto.phone.trim(),
              country_code: createNetEnquiryDto.country_code || '+91', // Extract country code
            },
            miles_office_id: milesOfficeId,
          },
      client_id: program.client_id,
      notes: `Lead created from net enquiry`,
      metadata: createNetEnquiryDto.enquiry_dto || {},
    };

    this.logger.log('Lead DTO prepared:', leadDto);

    try {
      this.logger.log('Handling lead creation');
      let leadResult;
      try {
        leadResult = await this.leadService.handleLead(leadDto, userId);
      } catch (error) {
        this.logger.error(
          `Error handling lead creation: ${error.message}`,
          error.stack,
        );
        //todo : Trigger some logging or alerting mechanism if needed
        throw new Error('Failed to handle lead creation');
      }

      lead = leadResult.lead;
      const isNew = leadResult.isNew;

      this.logger.log(`Lead handled. Lead ID: ${lead.id}, Is New: ${isNew}`);

      if (isNew) {
        this.logger.log(
          'New lead detected, setting default lead level and creating lead program interest',
        );
        const defaultLevel =
          await this.leadLevelService.findDefaultForNewEnquiry(
            program.client_id,
            programId,
          );
        this.logger.log('Default lead level found:', defaultLevel);

        const leadProgramInterestDto: CreateLeadProgramInterestDto = {
          lead_id: lead.id,
          program_id: programId,
          client_id: program.client_id,
          metadata: createNetEnquiryDto.enquiry_dto,
          lead_call_status: LeadCallStatus.NOTCALLED,
          lead_source_id: createNetEnquiryDto.lead_source_id,
          lead_level_id: defaultLevel?.id,
        };

        try {
          const leadProgramInterest =
            await this.leadProgramInterestService.create(
              leadProgramInterestDto,
              lead.id,
              userId,
            );
          try {
            await this.leadLevelService.createLevelChangeHistory({
              programInterestId: leadProgramInterest.id,
              fromLevelId: null,
              toLevelId: defaultLevel?.id,
              comments: 'Initial level assignment',
              transitionMethod: AllowedTransitionMethodEnum.System,
            });
          } catch (error) {
            this.logger.error(
              `Failed to create level change history for new lead: ${error.message}`,
              error.stack,
            );
          }
          netEnquiry.lead_program_interest = leadProgramInterest;
          netEnquiry.ne_type = NEType.NE;
          finalLeadProgInt = leadProgramInterest;

          if (defaultLevel) {
            this.logger.log(
              `Set default lead level ${defaultLevel.id} (${defaultLevel.name}) for new enquiry lead ${lead.id}`,
            );
          } else {
            this.logger.warn(
              `No default lead level found for client ${program.client_id}, lead ${lead.id} created without a level`,
            );
          }

          this.logger.log('Allocating SPOC asynchronously');
          if (spoc_phone) {
            this.allocateSpocDirectly(
              lead.id,
              spoc_phone,
              createNetEnquiryDto.lead_source_id,
              userId,
            );
          } else {
            this.allocateSpocAsynchronously(
              lead.id,
              createNetEnquiryDto.lead_source_id,
              userId,
            );
          }
        } catch (error) {
          this.logger.error(
            `Failed to create lead program interest: ${error.message}`,
            error.stack,
          );
          netEnquiry.ne_type = NEType.NE;
        }
      } else {
        this.logger.log(
          'Re-enquiry detected, updating lead call status and lead program interest',
        );

        lead.lead_call_status = LeadCallStatus.MHP;
        try {
          await this.leadService.update(lead.id, lead, userId);
          this.logger.log(
            `Lead call status updated to MHP for lead ID ${lead.id}`,
          );
        } catch (updateError) {
          this.logger.error(
            `Failed to update lead MHP state: ${updateError.message}`,
            updateError.stack,
          );
        }

        const leadProgramInterestDto: CreateLeadProgramInterestDto = {
          lead_id: lead.id,
          program_id: programId,
          client_id: program.client_id,
          metadata: {
            ...createNetEnquiryDto.enquiry_dto,
          },
          lead_source_id: createNetEnquiryDto.lead_source_id,
          lead_level_id: null,
          lead_call_status: LeadCallStatus.NOTCALLED,
        };

        try {
          this.logger.log(
            'Finding existing lead program interest for re-enquiry',
          );
          const existingInterest =
            await this.leadProgramInterestService.findByLeadIdAndProgramId(
              lead.id,
              programId,
            );

          if (existingInterest) {
            this.logger.log(
              'Existing lead program interest found:',
              existingInterest.id,
            );
            const currentLevelId = existingInterest.lead_level_id;
            let targetLevelId = currentLevelId;

            if (currentLevelId) {
              try {
                this.logger.log(
                  `Handling lead level transition for re-enquiry from level ${currentLevelId}`,
                );
                targetLevelId =
                  await this.leadLevelService.handleReEnquiryLevelTransition(
                    currentLevelId,
                    program.client_id,
                  );
                this.logger.log(
                  `Lead level transitioned from ${currentLevelId} to ${targetLevelId} for lead ${lead.id}`,
                );
              } catch (levelError) {
                this.logger.error(
                  `Failed to handle lead level transition: ${levelError.message}`,
                  levelError.stack,
                );
                targetLevelId = currentLevelId;
              }
            }

            existingInterest.call_status = LeadCallStatus.MHP;
            existingInterest.re_enquiry_date = new Date();
            existingInterest.lead_level_id = targetLevelId;
            existingInterest.metadata = {
              ...existingInterest.metadata,
              ...createNetEnquiryDto.enquiry_dto,
              updated_from: 'net_enquiry',
              update_mechanism: 're-enquiry',
            };

            if (!lead.owner_spoc_id) {
              if (spoc_phone) {
                this.allocateSpocDirectly(
                  lead.id,
                  spoc_phone,
                  createNetEnquiryDto.lead_source_id,
                  userId,
                );
              } else {
                this.allocateSpocAsynchronously(
                  lead.id,
                  createNetEnquiryDto.lead_source_id,
                  userId,
                );
              }
            }

            try {
              const updatedInterest =
                await this.leadProgramInterestService.update(
                  existingInterest.id,
                  existingInterest,
                  userId,
                );
              netEnquiry.lead_program_interest = updatedInterest;
              finalLeadProgInt = updatedInterest;
              this.logger.log(
                `Lead program interest updated for lead ${lead.id}`,
              );

              try {
                if (targetLevelId !== currentLevelId) {
                  await this.leadLevelService.createLevelChangeHistory({
                    programInterestId: updatedInterest.id,
                    fromLevelId: currentLevelId,
                    toLevelId: targetLevelId,
                    comments: `Re-enquiry level transition from ${currentLevelId} to ${targetLevelId}`,
                    transitionMethod: AllowedTransitionMethodEnum.System,
                  });
                }
              } catch (error) {
                this.logger.error(
                  `Failed to create level change history for re-enquiry: ${error.message}`,
                  error.stack,
                );
              }
            } catch (updateError) {
              this.logger.error(
                `Failed to update lead program interest: ${updateError.message}`,
                updateError.stack,
              );
              netEnquiry.lead_program_interest = existingInterest;
              finalLeadProgInt = existingInterest;
            }
          } else {
            this.logger.log(
              'No existing lead program interest found, creating a new one',
            );
            const defaultLevel =
              await this.leadLevelService.findDefaultForNewEnquiry(
                program.client_id,
                programId,
              );

            try {
              const newInterest = await this.leadProgramInterestService.create(
                {
                  ...leadProgramInterestDto,
                  lead_level_id: defaultLevel?.id,
                  metadata: {
                    ...leadProgramInterestDto.metadata,
                    created_from: 'net_enquiry',
                    creation_mechanism: 're-enquiry but no existing interest',
                  },
                },
                lead.id,
                userId,
              );

              netEnquiry.lead_program_interest = newInterest;
              finalLeadProgInt = newInterest;

              try {
                await this.leadLevelService.createLevelChangeHistory({
                  programInterestId: newInterest.id,
                  fromLevelId: null,
                  toLevelId: defaultLevel?.id,
                  comments: 'Initial level assignment',
                  transitionMethod: AllowedTransitionMethodEnum.System,
                });
              } catch (error) {
                this.logger.error(
                  `Failed to create level change history for new lead: ${error.message}`,
                  error.stack,
                );
              }

              if (defaultLevel) {
                this.logger.log(
                  `Set default lead level ${defaultLevel.id} (${defaultLevel.name}) for re-enquiry with no existing interest, lead ${lead.id}`,
                );
              } else {
                this.logger.warn(
                  `No default lead level found for client ${program.client_id}, lead program interest created without a level`,
                );
              }
            } catch (createError) {
              this.logger.error(
                `Failed to create new lead program interest: ${createError.message}`,
                createError.stack,
              );
            }
          }
        } catch (findError) {
          this.logger.error(
            `Error finding lead program interest: ${findError.message}`,
            findError.stack,
          );
          try {
            this.logger.log(
              'Creating fallback lead program interest due to error finding existing interest',
            );
            const defaultLevel =
              await this.leadLevelService.findDefaultForNewEnquiry(
                program.client_id,
                programId,
              );

            const newInterest = await this.leadProgramInterestService.create(
              {
                ...leadProgramInterestDto,
                lead_level_id: defaultLevel?.id,
                metadata: {
                  ...leadProgramInterestDto.metadata,
                  created_from: 'net_enquiry',
                  creation_mechanism: 'fallback',
                },
              },
              lead.id,
              userId,
            );
            netEnquiry.lead_program_interest = newInterest;
            finalLeadProgInt = newInterest;

            try {
              await this.leadLevelService.createLevelChangeHistory({
                programInterestId: newInterest.id,
                fromLevelId: null,
                toLevelId: defaultLevel?.id,
                comments: 'Initial level assignment',
                transitionMethod: AllowedTransitionMethodEnum.System,
              });
            } catch (error) {
              this.logger.error(
                `Failed to create level change history for new lead: ${error.message}`,
                error.stack,
              );
            }

            if (defaultLevel) {
              this.logger.log(
                `Set default lead level ${defaultLevel.id} (${defaultLevel.name}) for fallback scenario, lead ${lead.id}`,
              );
            }
          } catch (createError) {
            this.logger.error(
              `Failed to create fallback lead program interest: ${createError.message}`,
              createError.stack,
            );
          }
        }
        netEnquiry.ne_type = NEType.MHP;
      }
    } catch (leadError) {
      this.logger.error(
        `Error handling lead creation: ${leadError.message}`,
        leadError.stack,
      );
    }

    if (finalLeadProgInt && createNetEnquiryDto.register_for_webinar) {
      try {
        this.logger.log('Registering lead for all upcoming webinars');
        await this.webinarService.registerLeadForAllUpcomingWebinars(
          {
            leadProgramInterestId: finalLeadProgInt.id,
            campaignId: createNetEnquiryDto.campaign_id,
            clientId: program.client_id,
            programId: programId,
          },
          userId,
        );
        this.logger.log('Lead registered for webinars successfully');
      } catch (error) {
        this.logger.error(
          `Error registering lead for webinars: ${error.message}`,
          error.stack,
        );
      }
    }

    try {
      this.logger.log('Saving net enquiry to database');
      const savedNetEnquiry = await this.netEnquiryRepository.save(netEnquiry);
      this.logger.log(
        'Net enquiry saved successfully, ID:',
        savedNetEnquiry.id,
      );

      // Create lead history for net enquiry
      if (lead) {
        try {
          this.createNetEnquiryHistory(
            savedNetEnquiry,
            lead?.id,
            lead?.contact_id,
            program,
            campaign,
            userId,
          );
        } catch (historyError) {
          this.logger.error(
            `Failed to create lead history for net enquiry: ${historyError.message}`,
            historyError.stack,
          );
        }
      }

      return savedNetEnquiry;
    } catch (saveError) {
      this.logger.error(
        `Failed to save net enquiry: ${saveError.message}`,
        saveError.stack,
      );
      this.logger.log(
        'Attempting to save minimal net enquiry data as fallback',
      );

      try {
        const minimalNetEnquiry = this.netEnquiryRepository.create({
          ...createNetEnquiryDto,
          metadata: {
            ...createNetEnquiryDto,
            failed_save_reason: saveError.message,
            recovery_attempt: true,
          },
          created_by: userId,
        });
        const savedMinimal =
          await this.netEnquiryRepository.save(minimalNetEnquiry);
        this.logger.log(
          'Minimal net enquiry saved successfully as fallback, ID:',
          savedMinimal.id,
        );
        return savedMinimal;
      } catch (finalError) {
        this.logger.error(
          `CRITICAL: All attempts to save net enquiry data failed: ${finalError.message}`,
          finalError.stack,
        );
        throw new Error(
          'All attempts to save net enquiry data failed. Please contact support.',
        );
      }
    }
  }

  /**
   * Helper method to allocate a SPOC asynchronously without blocking the main flow
   * Uses Promise to run in background and logs any errors without affecting main process
   */
  private allocateSpocAsynchronously(
    leadId: number,
    sourceId: number,
    userId?: number,
  ): void {
    // Fire and forget - we don't await this promise
    Promise.resolve()
      .then(async () => {
        try {
          const allocateResult =
            await this.leadAllocationService.allocateCCSpocToLead({
              leadId,
              sourceId,
              allocatedBy: userId ? userId.toString() : 'system',
            });

          if (allocateResult) {
            this.logger.log(
              `Successfully allocated SPOC for Lead id: ${leadId}, Source ID: ${sourceId}`,
            );
          } else {
            this.logger.warn(
              `SPOC allocation returned false for Lead id: ${leadId}, Source ID: ${sourceId}`,
            );
          }
        } catch (error) {
          this.logger.error(
            `Error during async SPOC allocation for Lead id: ${leadId}, Source ID: ${sourceId}: ${error.message}`,
            error.stack,
          );
          //todo : Trigger some logging or alerting mechanism if needed

          // Error is caught and logged, but doesn't affect the main process
        }
      })
      .catch((error) => {
        this.logger.error(
          `Unhandled promise rejection during SPOC allocation for Lead id: ${leadId}, Source ID: ${sourceId}: ${error.message}`,
          error.stack,
        );
      });
  }

  private async allocateSpocDirectly(
    leadId: number,
    spocUserPhone: string,
    sourceId: number,
    userId?: number,
  ): Promise<void> {
    try {
      this.logger.log(
        `Starting direct SPOC allocation for Lead ID: ${leadId}, SPOC Phone Number: ${spocUserPhone}`,
      );

      // Get the lead first
      const lead = await this.leadService.findOne(leadId);
      if (!lead) {
        throw new Error(`Lead with ID ${leadId} not found`);
      }

      // Get the SPOC user
      const spocUser =
        await this.userService.findByOfficialNumber(spocUserPhone);
      const spocUserId = spocUser?.id;
      if (!spocUser) {
        this.logger.error(`SPOC User with phone ${spocUserPhone} not found`);
        throw new Error(`SPOC User with phone ${spocUserPhone} not found`);
      }

      // Update the lead's owner directly
      const updateResult =
        await this.leadAllocationService.updateLeadOwnerDirect(
          leadId,
          spocUserId,
          lead.client_id,
        );

      if (updateResult) {
        this.logger.log(
          `Successfully allocated SPOC User ID ${spocUserId} to Lead ID ${leadId}`,
        );

        // Record allocation history
        try {
          await this.leadAllocationHistoryService.recordAllocationHistoryNonTransactional(
            {
              lead,
              spocId: spocUserId,
              allocationType: AllocationTypeEnum.SYSTEM_REASSIGNMENT,
              allocatedBy: userId ? `user_${userId}` : 'system_direct',
              notes: `Direct SPOC assignment on ${new Date().toISOString()}`,
              clientId: lead.client_id,
              cityId: lead.contact?.miles_office_id,
              sourceId: sourceId,
            },
          );
        } catch (historyError) {
          this.logger.error(
            `Failed to record allocation history: ${historyError.message}`,
            historyError.stack,
          );
        }

        // Create lead history for direct allocation
        try {
          await this.leadHistoryService.createLeadHistory({
            action: ActionType.ENGAGEMENT,
            subAction: EngagementSubActionType.ENQUIRY,
            leadId,
            contactId: lead.contact_id,
            performedByUser: spocUser,
            performedBy: spocUserId,
            details: {
              allocationType: 'Direct Assignment',
              spocName: `${spocUser.first_name} ${spocUser.last_name}`,
              sourceId: sourceId,
              assignedBy: userId ? `User ID: ${userId}` : 'System',
            },
            metadata: {
              direct_allocation: true,
              spoc_user_id: spocUserId,
              source_id: sourceId,
              allocated_at: new Date(),
            },
          });
        } catch (historyError) {
          this.logger.error(
            `Failed to create allocation history: ${historyError.message}`,
            historyError.stack,
          );
        }
      } else {
        this.logger.warn(
          `Direct SPOC allocation returned false for Lead ID: ${leadId}, SPOC User ID: ${spocUserId}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `Error during direct SPOC allocation for Lead ID: ${leadId}, SPOC User Phone: ${spocUserPhone}: ${error.message}`,
        error.stack,
      );
    }
  }

  async findOne(id: number): Promise<NetEnquiry> {
    const netEnquiry = await this.netEnquiryRepository.findOne({
      where: { id },
      relations: ['program', 'campaign', 'leadProgramInterest'],
    });

    if (!netEnquiry) {
      throw new NotFoundException(`NetEnquiry with ID ${id} not found`);
    }

    return netEnquiry;
  }

  async update(
    id: number,
    updateNetEnquiryDto: UpdateNetEnquiryDto,
    userId?: number,
  ): Promise<NetEnquiry> {
    const netEnquiry = await this.findOne(id);

    // Update the entity
    Object.assign(netEnquiry, {
      ...updateNetEnquiryDto,
      updated_by: userId,
    });

    // Handle relation updates similar to the create method
    if (updateNetEnquiryDto.program_id) {
      try {
        const program = await this.programService.findOne(
          updateNetEnquiryDto.program_id,
        );
        netEnquiry.program = program;
      } catch (error) {
        this.logger.warn(
          `Program with ID ${updateNetEnquiryDto.program_id} not found: ${error.message}`,
          error.stack,
        );
      }
    }

    if (updateNetEnquiryDto.campaign_id) {
      try {
        const campaign = await this.campaignService.findOne(
          updateNetEnquiryDto.campaign_id,
        );
        netEnquiry.campaign = campaign;
      } catch (error) {
        this.logger.warn(
          `Campaign with ID ${updateNetEnquiryDto.campaign_id} not found: ${error.message}`,
          error.stack,
        );
      }
    }

    if (updateNetEnquiryDto.leadProgramInterest_id) {
      try {
        const leadProgramInterest =
          await this.leadProgramInterestService.findOne(
            updateNetEnquiryDto.leadProgramInterest_id,
          );
        netEnquiry.lead_program_interest = leadProgramInterest;
      } catch (error) {
        this.logger.warn(
          `LeadProgramInterest with ID ${updateNetEnquiryDto.leadProgramInterest_id} not found: ${error.message}`,
          error.stack,
        );
      }
    }

    try {
      return await this.netEnquiryRepository.save(netEnquiry);
    } catch (saveError) {
      this.logger.error(
        `Failed to update net enquiry: ${saveError.message}`,
        saveError.stack,
      );

      // Last resort - try to save minimal update without touching relations
      try {
        const simpleUpdate = {
          id: netEnquiry.id,
          updated_by: userId,
          metadata: {
            ...netEnquiry.metadata,
            failed_update_reason: saveError.message,
            recovery_attempt: true,
            original_update_data: updateNetEnquiryDto,
          },
        };

        return await this.netEnquiryRepository.save(simpleUpdate);
      } catch (finalError) {
        this.logger.error(
          `CRITICAL: All attempts to update net enquiry failed: ${finalError.message}`,
          finalError.stack,
        );
        throw new Error(
          'All attempts to update net enquiry data failed. Please contact support.',
        );
      }
    }
  }

  async remove(id: number): Promise<void> {
    try {
      const result = await this.netEnquiryRepository.delete({
        id,
      });

      if (result.affected === 0) {
        throw new NotFoundException(`NetEnquiry with ID ${id} not found`);
      }
    } catch (error) {
      this.logger.error(
        `Failed to delete net enquiry with ID ${id}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * Helper method to create lead history for net enquiry
   */
  private async createNetEnquiryHistory(
    netEnquiry: NetEnquiry,
    leadId: number,
    contactId: number,
    program: any,
    campaign: any,
    userId?: number,
  ): Promise<void> {
    const isReEnquiry = netEnquiry.ne_type === NEType.MHP;

    // Get all program interests for this lead to collect level information
    const allProgramInterests =
      await this.leadProgramInterestService.findAllByLeadId(leadId);

    // Format levels information
    const levels = allProgramInterests.map((interest) => ({
      initial: interest.lead_level?.name || null,
      next:
        interest.id === netEnquiry.lead_program_interest?.id
          ? netEnquiry.lead_program_interest.lead_level?.name || null
          : interest.lead_level?.name || null,
      type: interest.lead_level?.temperature || null,
      program: interest.program?.name || null,
      comment: interest.comments || null,
    }));

    const details = {
      name: `${netEnquiry?.first_name ?? ''} ${netEnquiry?.last_name ?? ''}`.trim(),
      program: program.name,
      campaignName: campaign?.name,
      enquiryType: isReEnquiry ? 'Re-Enquiry' : 'New Enquiry',
      source: netEnquiry?.lead_source?.name,
      comingFrom: netEnquiry?.coming_from,
    };

    this.leadHistoryService.createLeadHistory({
      action: ActionType.ENGAGEMENT,
      subAction: EngagementSubActionType.ENQUIRY,
      leadId,
      contactId,
      levels,
      performedByUser: userId ? await this.userService.findOne(userId) : null,
      performedBy: userId || null,
      details,
      metadata: netEnquiry,
    });
  }
}
