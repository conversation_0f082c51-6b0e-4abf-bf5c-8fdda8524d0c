import {
  IsBoolean,
  IsEnum,
  IsNumber,
  IsObject,
  IsOptional,
  IsString,
  IsUUID,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { InterestedToWorkInUs, NEType } from '../entities/net-enquiry.entity';
import { CreateContactDto } from 'src/modules/contacts/dto/create-contact.dto';
import { UUID } from 'crypto';
import { Type } from 'class-transformer';
import { LargeEventRegistrationDto } from './large-event-registration.dto';

export class CreateNetEnquiryDto {
  @ApiPropertyOptional({ description: 'First name of the enquirer' })
  @IsOptional()
  @IsString()
  first_name?: string;

  @ApiPropertyOptional({ description: 'Last name of the enquirer' })
  @IsOptional()
  @IsString()
  last_name?: string;

  @ApiPropertyOptional({ description: 'Lead source ID' })
  @IsOptional()
  @IsNumber()
  lead_source_id?: number;

  @ApiPropertyOptional({ description: 'Miles Office ID' })
  @IsOptional()
  @IsNumber()
  miles_office_id?: number;

  @ApiPropertyOptional({ description: 'Phone number' })
  @IsOptional()
  @IsString()
  phone?: string;

  @ApiPropertyOptional({ description: 'Spoc Phone number' })
  @IsOptional()
  @IsString()
  spoc_phone?: string;

  @ApiPropertyOptional({ description: 'Country code for phone number' })
  @IsOptional()
  @IsString()
  country_code?: string;

  @ApiPropertyOptional({ description: 'Email address' })
  @IsOptional()
  @IsString()
  email?: string;

  @ApiPropertyOptional({ description: 'City name' })
  @IsOptional()
  @IsString()
  city?: string;

  @ApiPropertyOptional({ description: 'NumVerify location' })
  @IsOptional()
  @IsString()
  num_verify_location?: string;

  @ApiPropertyOptional({ description: 'NumVerify country code' })
  @IsOptional()
  @IsString()
  num_verify_country_code?: string;

  @ApiPropertyOptional({ description: 'Program ID' })
  @IsOptional()
  @IsNumber()
  program_id?: number;

  @ApiPropertyOptional({ description: 'Origin of enquiry' })
  @IsOptional()
  @IsString()
  coming_from?: string;

  @ApiPropertyOptional({ description: 'Campaign ID' })
  @IsOptional()
  @IsNumber()
  campaign_id?: number;

  @ApiPropertyOptional({ description: 'Google Click ID' })
  @IsOptional()
  @IsString()
  gcl_id?: string;

  @ApiPropertyOptional({ description: 'WhatsApp opt-in status' })
  @IsOptional()
  @IsBoolean()
  whatsapp_opt_in?: boolean;

  @ApiPropertyOptional({ description: 'Register for webinar flag' })
  @IsOptional()
  @IsBoolean()
  register_for_webinar?: boolean;

  @ApiPropertyOptional({ description: 'Interested to work in US flag' })
  @IsOptional()
  @IsBoolean()
  intrested_to_work_in_us?: boolean;

  @ApiPropertyOptional({
    enum: InterestedToWorkInUs,
    description: 'Interested for work in US (enum)',
  })
  @IsOptional()
  @IsEnum(InterestedToWorkInUs)
  intrested_for_work_in_us?: InterestedToWorkInUs;

  @ApiPropertyOptional({ description: 'Graduation year' })
  @IsOptional()
  @IsString()
  graduation_year?: string;

  @ApiPropertyOptional({ description: 'Full page URL' })
  @IsOptional()
  @IsString()
  page_full_url?: string;

  @ApiPropertyOptional({ description: 'URL page path' })
  @IsOptional()
  @IsString()
  url_page_path?: string;

  @ApiPropertyOptional({ description: 'LinkedIn profile URL' })
  @IsOptional()
  @IsString()
  linkedin_url?: string;

  @ApiPropertyOptional({ description: 'Flag indicating welcome back status' })
  @IsOptional()
  @IsBoolean()
  isFromWelcomeBack?: boolean;

  @ApiPropertyOptional({ description: 'Lead Program Interest ID' })
  @IsOptional()
  @IsNumber()
  leadProgramInterest_id?: number;

  @ApiPropertyOptional({ description: 'Unique UUID' })
  @IsOptional()
  @IsUUID()
  uuid?: UUID;

  @ApiPropertyOptional({ enum: NEType, description: 'Net Enquiry type (enum)' })
  @IsOptional()
  @IsEnum(NEType)
  ne_type?: NEType;

  @ApiPropertyOptional({ description: 'Company website URL' })
  @IsOptional()
  @IsString()
  company_website_url?: string;

  @ApiPropertyOptional({ description: 'State' })
  @IsOptional()
  @IsString()
  state?: string;

  @ApiPropertyOptional({ description: 'Nationality' })
  @IsOptional()
  @IsString()
  nationality?: string;

  @ApiPropertyOptional({ description: 'Country of residence' })
  @IsOptional()
  @IsString()
  country_of_residence?: string;

  @ApiPropertyOptional({ description: 'Highest education level' })
  @IsOptional()
  @IsString()
  highest_education?: string;

  @ApiPropertyOptional({ description: 'Hospital name' })
  @IsOptional()
  @IsString()
  hospital_name?: string;

  @ApiPropertyOptional({ description: 'Additional enquiry data' })
  @IsOptional()
  @IsObject()
  enquiry_dto?: Record<string, any>;

  @ApiProperty({ description: 'Contact information' })
  @IsObject()
  @IsOptional()
  contact_info?: CreateContactDto;

  @IsString()
  @IsOptional()
  @ApiProperty()
  linked_in_url: string;

  @IsString()
  @IsOptional()
  @ApiProperty()
  clevertap_id: string;

  @ApiProperty()
  @IsOptional()
  level: number;

  @ApiProperty()
  @IsOptional()
  spoc_email: string;

  @ApiProperty()
  @IsOptional()
  @Type(() => Number)
  interested_webinar_id: number;

  @ApiProperty()
  @IsOptional()
  @Type(() => LargeEventRegistrationDto)
  large_event_registration_info: LargeEventRegistrationDto;

  @IsString()
  @IsOptional()
  @ApiProperty()
  campaign_tk: string;

  @IsNumber()
  @IsOptional()
  @ApiProperty()
  education_qualification: number;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  call_opt_in: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  email_opt_in: boolean;

  @IsBoolean()
  @IsOptional()
  @ApiProperty()
  sms_opt_in: boolean;

  @ApiProperty()
  @IsOptional()
  year_of_graduation: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  designation: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  organisation: string;
}
