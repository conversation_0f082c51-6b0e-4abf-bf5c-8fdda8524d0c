import { Modu<PERSON> } from '@nestjs/common';
import { RequestService } from './services/request.service';
import { RequestController } from './request.controller';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Request } from './entities/request.entity';
// import { RequestActionLog } from './entities/request-action-log.entity';
import { RequestWorkflow } from './entities/request-workflow.entity';
import { RequestWorkflowService } from './services/request-workflow.service';
import { Lead } from '@modules/leads/entities/lead.entity';
import { User } from '@modules/users/entities/user.entity';
import { RequestApproveActionService } from './services/request-approve-action.service';

@Module({
  imports: [TypeOrmModule.forFeature([Request, RequestWorkflow, Lead, User])],
  controllers: [RequestController],
  providers: [
    RequestService,
    RequestWorkflowService,
    RequestApproveActionService,
  ],
  exports: [RequestService],
})
export class RequestModule {}
