export enum RequestTypes {
  MERGE_LEAD = 'merge_lead',
  DOWNGRADE_LEVEL = 'downgrade_level',
  MARK_AS_DND = 'mark_as_dnd',
  TRANSFER_ENROLLMENT = 'transfer_enrollment',
}

export enum RequestStatus {
  PENDING = 'pending',
  IN_PROGESS = 'in_progress',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  CANCELLED = 'cancelled',
}

export enum ApprovalType {
  STANDARD_APPROVAL = 'standard_approval',
  ADMIN_APPROVAL = 'admin_approval',
}

export enum RejectionReturnStep {
  ORIGINATOR = 'originator',
  PREVIOUS_APPROVER = 'previous_approver',
  RAISE_NEW_REQUEST = 'raise_new_request',
}
