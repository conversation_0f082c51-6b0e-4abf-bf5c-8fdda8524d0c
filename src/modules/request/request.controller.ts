import { PermissionGuard } from '@modules/auth/guards/permission.guard';
import {
  Body,
  Controller,
  Get,
  Param,
  Post,
  Req,
  UseGuards,
} from '@nestjs/common';
import { ApiOperation, ApiParam, ApiTags } from '@nestjs/swagger';
import { RequestService } from './services/request.service';
import { GetRequestsToUserDto } from './dto/get-requets-to-user.dto';
import { AuthenticatedRequest } from '@modules/auth/interfaces/request.interface';
import { MergeLeadDto } from './dto/merge-lead.dto';
import { AcceptRejectRequestDto } from './dto/request-approval-action.dto';

@ApiTags('Request')
@Controller('request')
@UseGuards(PermissionGuard)
export class RequestController {
  constructor(private readonly requestService: RequestService) {}

  @Get()
  @ApiOperation({
    summary: 'Get all requests',
    description: 'This endpoint retrieves all requests made in the system.',
  })
  async getAllRequests() {
    return { message: 'This endpoint will return all requests' };
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get request enquiry by ID' })
  @ApiParam({ name: 'id', description: 'request ID' })
  async findOne(@Param('id') id: string) {
    console.log(id);
    // return this.requestService.findOne(+id);
  }

  @Post('requests-for-me')
  @ApiOperation({
    summary: 'Retrieving requests for particular user',
    description: 'This endpoint retrieves all requests for the logged in user.',
  })
  async getRequestsForMe(
    @Body() dto: GetRequestsToUserDto,
    @Req() req: AuthenticatedRequest,
  ) {
    return this.requestService.getRequestsToUser(dto, req.user);
  }

  @Post('requests-by-me')
  @ApiOperation({
    summary: 'Retrieving requests raised by particular user',
    description:
      'This endpoint retrieves all requests raised by logged in user.',
  })
  async getRequestsByMe() {
    return { message: 'This endpoint will return all requests' };
  }

  @Post(`approve-reject`)
  async acceptRejectRequest(
    @Body() payload: AcceptRejectRequestDto,
    @Req() req: AuthenticatedRequest,
  ) {
    return await this.requestService.requestApprovalAction(payload, req.user);
  }

  @Post(`merge-lead`)
  async mergeLeadReq(
    @Body() payload: MergeLeadDto,
    @Req() req: AuthenticatedRequest,
  ) {
    return await this.requestService.mergeLeadRequest(payload, req.user);
  }
}
