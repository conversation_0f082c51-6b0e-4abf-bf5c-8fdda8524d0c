import { ApiProperty, PartialType } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';
import { RequestStatus, RequestTypes } from '../enums/request.enum';
import { PaginationDto } from 'src/common/dto/pagination.dto';

export class GetRequestsToUserDto extends PartialType(PaginationDto) {
  @ApiProperty({ nullable: true })
  @IsOptional()
  @IsString()
  start_date: Date;

  @ApiProperty()
  @IsOptional()
  @IsString()
  end_date: Date;

  @ApiProperty({ nullable: true, enum: RequestStatus })
  @IsOptional()
  @IsEnum(RequestTypes)
  request_type: RequestTypes;

  @ApiProperty({ nullable: true })
  @IsOptional()
  @IsNumber()
  requested_by: number;

  @ApiProperty({ nullable: true, enum: RequestStatus })
  @IsOptional()
  @IsEnum(RequestStatus)
  requestStatus: RequestStatus;
}
