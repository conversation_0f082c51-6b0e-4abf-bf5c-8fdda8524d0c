import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { RequestStatus } from '../enums/request.enum';

export class AcceptRejectRequestDto {
  @ApiProperty()
  @IsNotEmpty()
  request_id: number;

  @IsNotEmpty()
  @IsString()
  action: RequestStatus.APPROVED | RequestStatus.REJECTED;

  @ApiProperty()
  @IsOptional()
  comment: string;
}
