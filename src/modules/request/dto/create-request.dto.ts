import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional } from 'class-validator';
import { ApprovalType, RequestTypes } from '../enums/request.enum';
import { User } from '@modules/users/entities/user.entity';

export class createRequestDto {
  @ApiProperty({ description: 'type of request' })
  @IsEnum(RequestTypes)
  request_type: RequestTypes;

  @ApiProperty({ description: 'ID of the lead' })
  @IsNotEmpty()
  lead_id: number;

  @ApiProperty({ description: 'ID of the lead' })
  @IsOptional()
  client_id?: number;

  @ApiProperty({ description: 'ID of the parent request if any' })
  @IsOptional()
  parent_request_id?: number;

  @ApiProperty({ description: 'ID of the work flow table' })
  @IsOptional()
  workflow_id?: number;

  @ApiPropertyOptional({ description: 'ID of the lead program interest' })
  @IsOptional()
  lead_program_interest_id?: number;

  @ApiPropertyOptional({ description: 'current level of request' })
  @IsNotEmpty()
  current_level: number;

  @ApiProperty({ description: 'request to users' })
  @IsNotEmpty()
  request_to: User[];

  @ApiProperty({ description: 'requested user' })
  @IsNotEmpty()
  request_from: User;

  @ApiProperty({ description: 'type of request approval' })
  @IsNotEmpty()
  approval_type: ApprovalType;

  @ApiPropertyOptional({ description: 'initial comment by requester' })
  @IsOptional()
  initial_comment: string;

  @ApiPropertyOptional({ description: 'existing data while raising request' })
  @IsOptional()
  previous_value?: object;

  @ApiPropertyOptional({
    description: 'new data to be approved in raising request',
  })
  @IsOptional()
  new_value?: object;
}
