import { BaseEntity } from 'src/common/entities/base.entity';
import {
  Column,
  Entity,
  Index,
  JoinColumn,
  JoinTable,
  ManyToMany,
  ManyToOne,
  OneToMany,
} from 'typeorm';
import { ApprovalType, RequestStatus } from '../enums/request.enum';
import { User } from '@modules/users/entities/user.entity';
import { RequestWorkflow } from './request-workflow.entity';
import { Lead } from '@modules/leads/entities/lead.entity';
import { LeadProgramInterest } from '@modules/leads/entities/lead-program-interest.entity';

@Entity()
export class Request extends BaseEntity {
  @ManyToOne(() => RequestWorkflow, { onDelete: 'NO ACTION' })
  @JoinColumn({ name: 'request_workflow_id' })
  request_workflow: RequestWorkflow;

  @Column({ type: 'integer' })
  request_workflow_id: number;

  @ManyToOne(() => Lead, { onDelete: 'NO ACTION' })
  @JoinColumn({ name: 'lead_id' })
  lead: Lead;

  @Column({ type: 'integer', nullable: false })
  lead_id: number;

  @ManyToOne(() => LeadProgramInterest, { onDelete: 'NO ACTION' })
  @JoinColumn({ name: 'lead_program_interest_id' })
  lead_program_interest: LeadProgramInterest;

  @Column({ type: 'integer', nullable: true })
  lead_program_interest_id: number;

  @Column({ type: 'jsonb', nullable: true })
  previous_value: Record<string, any>;

  @Column({ type: 'jsonb', nullable: true })
  new_value: Record<string, any>;

  @Column({ type: 'enum', enum: ApprovalType, nullable: false })
  approval_type: ApprovalType;

  @Column({ type: 'integer', nullable: false })
  current_level: number;

  @Index()
  @ManyToOne(() => User, (user) => user.id)
  request_from: User;

  @ManyToMany(() => User)
  @JoinTable()
  request_to: User[];

  @Index()
  @ManyToOne(() => User, (user) => user.id)
  approved_by: User;

  @Column({ type: 'varchar', nullable: true })
  initial_comment: string;

  @Column({ type: 'varchar', nullable: true })
  approver_comment: string;

  @ManyToOne(() => Request, { nullable: true })
  @JoinColumn({ name: 'parent_request_id' })
  parent_request: Request;

  @OneToMany(() => Request, (request) => request.parent_request)
  child_requests: Request[];

  @Column({
    type: 'enum',
    enum: RequestStatus,
    default: RequestStatus.PENDING,
    nullable: false,
  })
  request_status: RequestStatus;

  @Column({ type: 'int', array: true, nullable: true })
  view_roles: number[];

  @Column({ type: 'int', array: true, nullable: true })
  write_roles: number[];
}
