import { Column, Entity } from 'typeorm';
import { ClientAwareEntity } from 'src/common/entities/client-aware.entity';
import { RejectionReturnStep, RequestTypes } from '../enums/request.enum';

@Entity()
export class RequestWorkflow extends ClientAwareEntity {
  @Column({ type: 'enum', enum: RequestTypes, nullable: false })
  request_type: RequestTypes;

  @Column({
    type: 'enum',
    enum: RejectionReturnStep,
    nullable: true,
    default: RejectionReturnStep.RAISE_NEW_REQUEST,
  })
  rejection_return_level: RejectionReturnStep;

  @Column({ type: 'integer', nullable: false })
  required_levels: number;
}
