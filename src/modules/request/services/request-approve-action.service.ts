import { Injectable } from '@nestjs/common';
import { Request } from '../entities/request.entity';
import {
  RejectionReturnStep,
  RequestStatus,
  RequestTypes,
} from '../enums/request.enum';
import { In, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';

@Injectable()
export class RequestApproveActionService {
  constructor(
    @InjectRepository(Request)
    private readonly requestRepository: Repository<Request>,
  ) {}

  async approveRequest(request: Request) {
    switch (request.request_workflow.request_type) {
      case RequestTypes.MERGE_LEAD: {
        // do merge lead action here
        break;
      }
      default: {
        throw new Error('Wrong request type');
      }
    }
  }

  async rejectRequest(request: Request) {
    const rejectionStep = request.request_workflow.rejection_return_level;
    const parentRequestId = request.parent_request?.id ?? request.id;
    switch (rejectionStep) {
      case RejectionReturnStep.ORIGINATOR:
        // Set first level request to PENDING
        const rootRequest = await this.findRootRequest(request);
        if (rootRequest) {
          rootRequest.request_status = RequestStatus.PENDING;
          rootRequest.is_active = true;
          await this.requestRepository.save(rootRequest);

          const allRequests = await this.requestRepository.find({
            relations: ['parent_request'],
          });

          function isDescendantOf(
            request: Request,
            rootId: number,
            requestMap: Map<number, Request>,
          ): boolean {
            let current = request;
            while (current) {
              if (current.id === rootId) return true;
              current = current.parent_request
                ? requestMap.get(current.parent_request.id)
                : null;
            }
            return false;
          }

          const requestMap = new Map(allRequests.map((r) => [r.id, r]));
          const toDeactivate = allRequests.filter(
            (r) =>
              r.id !== rootRequest.id &&
              isDescendantOf(r, rootRequest.id, requestMap),
          );
          console.log(toDeactivate, 'to deactive');
          // Update all descendants to is_active = false
          if (toDeactivate.length > 0) {
            await this.requestRepository.update(
              { id: In(toDeactivate.map((r) => r.id)) },
              { is_active: false },
            );
          }
        }
        break;

      case RejectionReturnStep.PREVIOUS_APPROVER:
        // Set is_active of current level request to false
        request.is_active = false;
        await this.requestRepository.save(request);

        // Set previous level request to PENDING
        if (request.current_level > 1) {
          console.log(parentRequestId);
          const prevLevelRequest = await this.requestRepository.findOne({
            where: {
              id: parentRequestId,
              current_level: request.current_level - 1,
            },
          });
          if (prevLevelRequest) {
            prevLevelRequest.request_status = RequestStatus.PENDING;
            prevLevelRequest.is_active = true;
            await this.requestRepository.save(prevLevelRequest);
          }
        }
        break;

      case RejectionReturnStep.RAISE_NEW_REQUEST:
      default:
        // Reject current level request
        request.request_status = RequestStatus.REJECTED;
        request.is_active = false;
        await this.requestRepository.save(request);
        break;
    }
    return request;
  }

  async findRootRequest(request: Request): Promise<Request> {
    let current = request;
    while (current.parent_request) {
      current = await this.requestRepository.findOne({
        where: { id: current.parent_request.id },
        relations: ['parent_request'],
      });
      if (!current) break; // safety check
    }
    return current;
  }
}
