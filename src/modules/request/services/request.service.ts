import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
// import { RequestActionLog } from '../entities/request-action-log.entity';
import { ILike, Repository } from 'typeorm';
import { createRequestDto } from '../dto/create-request.dto';
import { Request } from '../entities/request.entity';
import { RequestWorkflowService } from './request-workflow.service';
import {
  ApprovalType,
  RequestStatus,
  RequestTypes,
} from '../enums/request.enum';
import { GetRequestsToUserDto } from '../dto/get-requets-to-user.dto';
import { MergeLeadDto } from '../dto/merge-lead.dto';
import { Lead } from '@modules/leads/entities/lead.entity';
import { User } from '@modules/users/entities/user.entity';
import { AcceptRejectRequestDto } from '../dto/request-approval-action.dto';
import { RequestApproveActionService } from './request-approve-action.service';

@Injectable()
export class RequestService {
  constructor(
    // @InjectRepository(RequestActionLog)
    // private readonly requestActionLogRepository: Repository<RequestActionLog>,
    @InjectRepository(Request)
    private readonly requestRepository: Repository<Request>,
    private readonly requestWorkflowService: RequestWorkflowService,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(Lead)
    private leadRepository: Repository<Lead>,
    private readonly requestApproveService: RequestApproveActionService,
  ) {}

  /**
   * Create request
   */
  async createRequest(dto: createRequestDto) {
    try {
      const request = new Request();
      request.previous_value = dto.previous_value;
      request.new_value = dto.new_value;
      request.request_workflow_id = dto.workflow_id
        ? dto.workflow_id
        : await this.requestWorkflowService.getWorkflowFromRequestTypeAndClient(
            dto.request_type,
            dto.client_id,
          );
      request.current_level = dto.current_level;
      request.request_from = dto.request_from;
      // Fetch User entities for request_to
      let requestToUsers: User[] = [];
      if (Array.isArray(dto.request_to)) {
        requestToUsers = await this.userRepository.findByIds(
          dto.request_to.map((u) => (typeof u === 'object' ? u.id : u)),
        );
      } else if (dto.request_to) {
        const user = await this.userRepository.findOne(dto.request_to);
        if (user) requestToUsers = [user];
      }
      request.request_to = requestToUsers;
      // request.request_to = dto.request_to; // need to change the logic if request don't need to go to manager
      request.initial_comment = dto.initial_comment;
      request.lead_id = dto.lead_id;
      request.lead_program_interest_id = dto?.lead_program_interest_id;
      request.approval_type = dto.approval_type;
      // Set parent_request if provided
      if (dto.parent_request_id) {
        const parentRequest = await this.requestRepository.findOne({
          where: { id: dto.parent_request_id },
        });
        if (parentRequest) {
          request.parent_request = parentRequest;
        }
      }
      if (dto.approval_type === ApprovalType.STANDARD_APPROVAL) {
        request.request_status = RequestStatus.PENDING;
      } else if (dto.approval_type === ApprovalType.ADMIN_APPROVAL) {
        request.request_status = RequestStatus.APPROVED;
      }
      const result = await this.requestRepository.save(request);
      return result;
    } catch (error) {
      throw new Error(`failed to create request: ${error.message}`);
    }
  }

  /**
   * Get requests to user
   */
  async getRequestsToUser(dto: GetRequestsToUserDto, user: any) {
    const baseCondition = {
      status: true,
      is_active: true,
      request_status: RequestStatus.PENDING,
      request_workflow: dto.request_type
        ? { request_type: dto.request_type }
        : undefined,
      request_from: dto.requested_by ? { id: dto.requested_by } : undefined,
      request_to: { id: user.id },
    };

    const condition = dto.pattern
      ? [
          {
            ...baseCondition,
            request_from: { firstName: ILike(`%${dto.pattern}%`) },
          },
          {
            ...baseCondition,
            request_from: { lastName: ILike(`%${dto.pattern}%`) },
          },
          {
            ...baseCondition,
            request_from: { employee_id: ILike(`%${dto.pattern}%`) },
          },
        ]
      : baseCondition;
    const count = await this.requestRepository.count({
      where: condition,
    });
    const skip = (dto.page - 1) * dto.size;
    const data = await this.requestRepository.find({
      where: condition,
      skip: skip,
      take: dto.size,
      order: { created_at: 'DESC' },
      relations: {
        request_from: true,
        request_to: true,
        approved_by: true,
        request_workflow: true,
        lead: true,
      },
    });

    const requests = [];
    for (const request of data) {
      requests.push({
        id: request.id,
        request_type: request.request_workflow.request_type,
        request_status: request.request_status,
        request_from: request.request_from,
        request_to: request.request_to,
        created_at: request.created_at,
        comments: request.initial_comment,
        lead: request.lead,
      });
    }
    return { data: requests, count };
  }

  /**
   * Accepting or rejecting request
   */
  async requestApprovalAction(dto: AcceptRejectRequestDto, user: any) {
    try {
      const request = await this.requestRepository.findOne({
        where: { id: dto.request_id },
        relations: {
          request_workflow: true,
          request_from: true,
          request_to: true,
          parent_request: true,
          child_requests: true,
          lead: true,
        },
      });
      if (
        request.request_status === RequestStatus.APPROVED ||
        request.request_status === RequestStatus.REJECTED
      ) {
        throw new HttpException(
          `This request is already ${request.request_status}`,
          HttpStatus.BAD_REQUEST,
        );
      }
      request.request_status = dto.action;
      request.approver_comment = dto.comment;
      request.approved_by = user;
      if (dto.action === RequestStatus.APPROVED) {
        if (
          request.request_workflow.required_levels === request.current_level
        ) {
          await this.requestApproveService.approveRequest(request);
        } else {
          const payload = {
            previous_value: request.previous_value,
            new_value: request.new_value,
            request_type: RequestTypes.MERGE_LEAD,
            client_id: request.lead.client_id,
            current_level: request.current_level + 1,
            request_from: request.request_from,
            request_to: [user.manager_id],
            initial_comment: request.initial_comment,
            approval_type: ApprovalType.STANDARD_APPROVAL,
            lead_id: request.lead_id,
            parent_request_id: request.id,
          };
          await this.createRequest(payload);
        }
      } else if (dto.action === RequestStatus.REJECTED) {
        await this.requestApproveService.rejectRequest(request);
      }
      return await this.requestRepository.save(request);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      const action =
        dto.action === RequestStatus.APPROVED ? 'Approve' : 'Reject';
      throw new Error(`Failed to ${action}: ${error.message}`);
    }
  }

  /**
   * Merge Lead Request
   */
  async mergeLeadRequest(dto: MergeLeadDto, user: any) {
    try {
      if (dto.leadId1 === dto.leadId2) {
        throw new HttpException(
          'Both lead IDs are the same',
          HttpStatus.BAD_REQUEST,
        );
      }
      const lead1 = await this.leadRepository.findOne({
        where: { id: dto.leadId1 },
      });
      const lead2 = await this.leadRepository.findOne({
        where: { id: dto.leadId2 },
      });
      if (!lead1 || !lead2) {
        throw new HttpException(
          'One or both leads not found',
          HttpStatus.NOT_FOUND,
        );
      }
      const previous_value = {
        leadId1: dto.leadId1,
        leadId2: dto.leadId2,
      };
      const new_value = {
        leadId: dto.leadId1,
        updated_by: user.id,
      };
      const mergeRequestDto = {
        previous_value,
        new_value,
        request_type: RequestTypes.MERGE_LEAD,
        client_id: lead1.client_id,
        current_level: 1,
        request_from: user,
        request_to: [user.manager_id],
        initial_comment: dto.comment,
        approval_type: ApprovalType.STANDARD_APPROVAL,
        lead_id: dto.leadId1,
      };
      await this.createRequest(mergeRequestDto);
      return {
        message: 'Merge lead request has beeen sent succesfully for approval',
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new Error(
        `Failed to create request for merging lead: ${error.message}`,
      );
    }
  }
}
