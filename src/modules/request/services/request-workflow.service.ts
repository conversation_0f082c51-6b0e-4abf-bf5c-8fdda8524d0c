import { Injectable } from '@nestjs/common';
import { RequestWorkflow } from '../entities/request-workflow.entity';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { RequestTypes } from '../enums/request.enum';

@Injectable()
export class RequestWorkflowService {
  constructor(
    @InjectRepository(RequestWorkflow)
    private readonly requestWorkflowRepository: Repository<RequestWorkflow>,
  ) {}

  async getWorkflowFromRequestTypeAndClient(
    request_type: RequestTypes,
    client_id: number,
  ) {
    try {
      const workflow = await this.requestWorkflowRepository.findOne({
        select: ['id'],
        where: {
          request_type,
          client_id,
        },
      });
      return workflow ? workflow.id : null;
    } catch (error) {
      throw error;
    }
  }
}
