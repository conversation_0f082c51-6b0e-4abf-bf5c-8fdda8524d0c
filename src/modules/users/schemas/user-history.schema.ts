import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document } from 'mongoose';

export enum UserAction {
  create = 'create',
  update = 'update',
  deactivate = 'deactivate',
  activate = 'activate',
}

@Schema({ collection: 'user_history', timestamps: true })
export class UserHistory extends Document {
  @Prop({ type: Number, required: true })
  userId: number;

  @Prop({ type: String, enum: UserAction, required: true })
  action: UserAction;

  @Prop({ type: Number, required: false })
  performedBy: number;

  @Prop({ type: Object, required: true, default: { first_name: 'System' } })
  performedByUser: object;

  @Prop({ type: Date, required: true })
  date: Date;

  @Prop({ type: Object, required: false })
  data: object;

  @Prop({ type: Object, required: false })
  previousUserData: object;

  @Prop({ type: Object, required: false })
  metadata: object;
}

export const UserHistorySchema = SchemaFactory.createForClass(UserHistory);
