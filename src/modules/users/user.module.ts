import { Global, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { UserController } from './user.controller';
import { User } from './entities/user.entity';
import { UserService } from './services/user.service';
import { UserHierarchyService } from './services/user-hierarchy.service';
import { UserClientModule } from '../user-clients/user-client.module';
import { CacheModule } from 'src/common/cache/cache.module';
import { PermissionGuard } from '@modules/auth/guards/permission.guard';
import { RoleModule } from '@modules/roles/role.module';
import { UserClient } from '@modules/user-clients/entities/user-client.entity';
import { UserClientRole } from '@modules/user-client-roles/entities/user-client-role.entity';
import { UserCity, UserSource } from '@modules/lead-allocations/entities';
import { Role } from '@modules/roles/enities/role.entity';
import { MongooseModule } from '@nestjs/mongoose';
import { UserHistorySchema } from './schemas/user-history.schema';
import { UserHistoryService } from './services/user-history.service';

@Global()
@Module({
  imports: [
    TypeOrmModule.forFeature([
      User,
      UserClient,
      UserClientRole,
      UserCity,
      Role,
      UserSource,
    ]),
    MongooseModule.forFeature([
      { name: 'UserHistory', schema: UserHistorySchema },
    ]),
    UserClientModule,
    CacheModule,
    RoleModule,
  ],
  controllers: [UserController],
  providers: [
    UserService,
    UserHierarchyService,
    PermissionGuard,
    UserHistoryService,
  ],
  exports: [UserService, UserHierarchyService],
})
export class UserModule {}
