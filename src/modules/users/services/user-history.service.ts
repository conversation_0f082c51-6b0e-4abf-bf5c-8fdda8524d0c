import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { UserHistory } from '../schemas/user-history.schema';

@Injectable()
export class UserHistoryService {
  constructor(
    @InjectModel('UserHistory') private userHistoryModel: Model<UserHistory>,
  ) {}

  /**
   * Create user action history
   */
  async createUserHistory(payload: {
    userId: number;
    action: string;
    performedBy: number;
    performedByUser: any;
    date: Date;
    data: any;
    metadata?: any;
    previousUserData?: any;
  }) {
    try {
      return await this.userHistoryModel.create(payload);
    } catch (error) {
      console.error('Error creating lead history:', error);
    }
  }
}
