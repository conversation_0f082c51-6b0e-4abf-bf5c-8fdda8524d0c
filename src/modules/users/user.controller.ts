import {
  Controller,
  Get,
  Post,
  Body,
  HttpStatus,
  HttpCode,
  Patch,
  Param,
  UseGuards,
  Req,
  Query,
  UnauthorizedException,
} from '@nestjs/common';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserWithRelationsDto } from './dto/update-user.dto';
import { User } from './entities/user.entity';
import { UserService } from './services/user.service';
import { UserHierarchyService } from './services/user-hierarchy.service';
import { ClientAccessGuard } from '../user-clients/guards/client-access.guard';
import { ApiResource } from '@modules/auth/decorators/resource.decorator';
import { PermissionGuard } from '@modules/auth/guards/permission.guard';
import { Action, Resource } from '@modules/permissions/enums/permission.enum';
import { AuthenticatedRequest } from '@modules/auth/interfaces/request.interface';
import { FilterUserDto } from './dto/fiter-user.dto';
import {
  ApiTags,
  ApiOperation,
  ApiBody,
  ApiParam,
  ApiQuery,
  ApiResponse,
} from '@nestjs/swagger';
import { PaginatedResponse } from 'src/common/interfaces/pagination.interface';
import { GetUsersDto } from './dto/get-user.dto';
import { RequirePermission } from '@modules/auth/decorators/permission.decorator';

@ApiTags('users')
@Controller('users')
@UseGuards(ClientAccessGuard)
@ApiResource(Resource.USER)
@UseGuards(PermissionGuard)
export class UserController {
  constructor(
    private readonly usersService: UserService,
    private readonly userHierarchyService: UserHierarchyService,
  ) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Create a new user with roles, business units, and cities',
  })
  @ApiBody({ type: CreateUserDto })
  async create(
    @Body() createUserDto: CreateUserDto,
    @Req() req: AuthenticatedRequest,
  ) {
    // Extract the relationship fields that should be handled separately
    const { business_units, ...userData } = createUserDto;

    // Create the user and handle the relationships
    const user = await this.usersService.createUserWithRelations(
      userData,
      business_units,
      req.user?.id,
    );
    return {
      success: true,
      message: 'User created successfully',
      data: user,
    };
  }

  @Patch(':id/update-user')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: 'Update a user with their roles, business units, and cities',
    description: 'Updates a user and their associated relationships',
  })
  @ApiParam({ name: 'id', description: 'User ID to update' })
  @ApiBody({ type: UpdateUserWithRelationsDto })
  async updateWithRelations(
    @Param('id') id: string,
    @Body() updateDto: UpdateUserWithRelationsDto,
    @Req() req: AuthenticatedRequest,
  ) {
    // Extract the relationship fields that should be handled separately
    const { business_units, ...userData } = updateDto;

    // Update the user and handle the relationships
    const user = await this.usersService.updateUserWithRelations(
      +id,
      userData,
      business_units,
      req.user?.id,
    );

    return {
      success: true,
      message: 'User updated successfully',
      data: user,
    };
  }

  @RequirePermission({ resource: Resource.USER, action: Action.READ })
  @Post('all')
  @ApiOperation({
    summary: 'Get all users with optional filters',
    description: `Filter users by various criteria:
  - isActive: Filter by active status
  - pattern: Search in first name, last name, and email
  - business_unit_with_roles: Filter by business unit and role combinations
  - city_ids: Filter by associated cities`,
  })
  @ApiBody({ type: GetUsersDto, required: false })
  async getAllUsers(
    @Body() filterDto: GetUsersDto,
    @Req() req: AuthenticatedRequest,
  ): Promise<PaginatedResponse<any>> {
    const clientId = req.user?.currentClientId;
    return this.usersService.getAllUsers(filterDto, clientId);
  }

  @Get('/managers')
  @ApiOperation({ summary: 'Get all managers with optional filters' })
  @ApiQuery({ type: FilterUserDto, required: false })
  async getAllManagers(
    @Query() filterDto: FilterUserDto,
    @Req() req: AuthenticatedRequest,
  ) {
    const clientId = req.user?.currentClientId;
    return this.usersService.getAllManagers(filterDto, clientId);
  }

  @Get('/me')
  @ApiOperation({
    summary: 'Get current user info with roles and business units',
  })
  async getUserInfo(@Req() req: AuthenticatedRequest) {
    const userId = req.user?.id;
    const currentClientId = req.user?.currentClientId;

    if (!userId) {
      throw new UnauthorizedException(
        'User not authenticated or missing client information',
      );
    }

    return this.usersService.getUserDetailedInfo(req, userId, currentClientId);
  }

  @Get('/hierarchy')
  @ApiOperation({
    summary: 'Get hierarchical structure based on user role',
    description:
      'Returns hierarchical data: Admin sees all GMs with their cities and SPOCs; GMs see their cities and SPOCs; SPOCs see only themselves',
  })
  @ApiResponse({
    status: 200,
    description: 'Hierarchical structure according to user role',
  })
  async getUserHierarchy(@Req() req: AuthenticatedRequest) {
    const clientId = req.user?.currentClientId;
    const userId = req.user?.id;
    // Using the dedicated UserHierarchyService instead of UserService
    return this.userHierarchyService.getUserHierarchy(userId, clientId);
  }

  @Get('/gms-with-spocs')
  @ApiOperation({
    summary: 'Get all GMs with their associated SPOCs',
    description:
      'Returns a list of GMs and for each GM, shows their associated SPOCs based on the manager-subordinate relationship',
  })
  @ApiResponse({
    status: 200,
    description: 'List of GMs with their SPOCs',
  })
  async getGMsWithSPOCs(@Req() req: AuthenticatedRequest) {
    const clientId = req.user?.currentClientId;

    if (!clientId) {
      throw new UnauthorizedException('Client information is required');
    }

    return this.usersService.getGMsWithSPOCs(clientId);
  }

  @Patch(':id/toggle-status')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: 'Activate or deactivate a user by ID' })
  @ApiParam({ name: 'id', description: 'User ID to toggle status' })
  async toggleUserStatus(
    @Param('id') id: string,
    @Req() req: AuthenticatedRequest,
  ): Promise<User> {
    const user = req.user;
    return this.usersService.toggleUserStatus(+id, user.id);
  }
}
