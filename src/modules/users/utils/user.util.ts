/**
 * Utility function to format employee ID based on environment
 * @param employeeId - Original employee ID
 * @returns Formatted employee ID with environment prefix
 */
export function formatEmployeeId(employeeId?: string): string | undefined {
  if (!employeeId) {
    return undefined;
  }

  const environment = process.env.NODE_ENV || 'development';

  switch (environment) {
    case 'production':
      return `MF3_${employeeId}`;
    case 'uat':
    case 'pre-prod':
      return `MF_PRO_${employeeId}`;
    default:
      // For development and other environments, return as-is
      return employeeId;
  }
}

/**
 * Utility function to format GM-SPOC raw query results into structured data
 * @param rawResults - Raw query results from database
 * @returns Object containing formatted GMs with their SPOCs and metadata
 */
export function formatGMsWithSPOCs(rawResults: any): {
  formattedData: any;
  totalSpocs: number;
} {
  if (!rawResults || rawResults.length === 0) {
    return {
      formattedData: [],
      totalSpocs: 0,
    };
  }

  // Use Map for O(1) lookup performance
  const gmMap = new Map<number, any>();
  let totalSpocs = 0;

  for (const row of rawResults) {
    const gmId = row.gm_id;

    // Initialize GM if not exists
    if (!gmMap.has(gmId)) {
      gmMap.set(gmId, {
        id: gmId,
        first_name: row.gm_first_name || '',
        last_name: row.gm_last_name || '',
        email: row.gm_email || '',
        employee_id: row.gm_employee_id || '',
        official_number: row.gm_official_number || 'N/A',
        virtual_number: row.gm_virtual_number || 'N/A',
        role: 'GM',
        spocs: [],
        spoc_count: 0,
      });
    }

    // Add SPOC if exists (LEFT JOIN can have null SPOCs)
    if (row.spoc_id) {
      const existingGM = gmMap.get(gmId)!;

      // Check if SPOC already exists to avoid duplicates
      const spocExists = existingGM.spocs.some(
        (spoc) => spoc.id === row.spoc_id,
      );

      if (!spocExists) {
        existingGM.spocs.push({
          id: row.spoc_id,
          first_name: row.spoc_first_name || '',
          last_name: row.spoc_last_name || '',
          email: row.spoc_email || '',
          employee_id: row.spoc_employee_id || '',
          official_number: row.spoc_official_number || 'N/A',
          virtual_number: row.spoc_virtual_number || 'N/A',
        });
        totalSpocs++;
      }
    }
  }

  // Convert Map to array and update spoc_count
  const formattedData = Array.from(gmMap.values()).map((gm) => ({
    ...gm,
    spoc_count: gm.spocs.length,
  }));

  return {
    formattedData,
    totalSpocs,
  };
}

/**
 * Utility function to create standardized response for GM-SPOC queries
 * @param formattedData - Formatted GM data with SPOCs
 * @param totalSpocs - Total number of SPOCs
 * @param clientId - Client ID for metadata
 * @returns Standardized response object
 */
export function createGMSPOCResponse(
  formattedData: any,
  totalSpocs: number,
  clientId: number,
) {
  return {
    data: formattedData,
    meta: {
      total: formattedData.length,
      total_gms: formattedData.length,
      total_spocs: totalSpocs,
      client_id: clientId,
      ...(formattedData.length === 0 && {
        message: 'No GMs found for this client',
      }),
    },
  };
}
