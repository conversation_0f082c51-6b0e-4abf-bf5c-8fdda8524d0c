import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsInt, IsNotEmpty, IsOptional, IsString } from 'class-validator';

export class CreateEnrollmentDto {
  @ApiProperty({ description: 'The ID of the lead program interest' })
  @IsNotEmpty()
  @IsInt()
  lead_program_interest_id: number;

  @ApiProperty({ description: 'The ID of the user who enrolled' })
  @IsNotEmpty()
  @IsInt()
  enrolled_by_id: number;

  @ApiProperty({ description: 'The date of enrollment' })
  @IsNotEmpty()
  enrollment_date: Date;

  @ApiPropertyOptional({ description: 'Additional notes for the enrollment' })
  @IsOptional()
  @IsString()
  notes?: string;
}
