import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { EnrollmentController } from './enrollment.controller';
import { EnrollmentService } from './services/enrollment.service';
import { Enrollment } from './entities/enrollment.entity';
import { LeadProgramInterest } from '@modules/leads/entities/lead-program-interest.entity';
import { LeadModule } from '@modules/leads/lead.module';

@Module({
  imports: [
    TypeOrmModule.forFeature([Enrollment, LeadProgramInterest]),
    forwardRef(() => LeadModule),
  ],
  controllers: [EnrollmentController],
  providers: [EnrollmentService],
  exports: [EnrollmentService],
})
export class EnrollmentModule {}
