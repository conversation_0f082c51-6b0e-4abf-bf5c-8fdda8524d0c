import { LeadProgramInterest } from '@modules/leads/entities/lead-program-interest.entity';
import { User } from '@modules/users/entities/user.entity';
import { ClientAwareEntity } from 'src/common/entities/client-aware.entity';
import { Entity, Column, ManyToOne, Join<PERSON><PERSON>umn } from 'typeorm';
import { DefaulterReason } from '../types/defaulter.type';

@Entity()
export class Enrollment extends ClientAwareEntity {
  @ManyToOne(() => User)
  @JoinColumn({ name: 'enrolled_by_id' })
  enrolled_by: User;

  @Column({ name: 'enrolled_by_id' })
  enrolled_by_id: string;

  @Column({ type: 'timestamp' })
  enrollment_date: Date;

  @ManyToOne(() => LeadProgramInterest)
  @JoinColumn({ name: 'lead_program_interest_id' })
  lead_program_interest: LeadProgramInterest;

  @Column({ name: 'lead_program_interest_id' })
  lead_program_interest_id: string;

  @Column({ type: 'boolean', default: false })
  is_defaulter: boolean;

  @Column({ type: 'timestamp', nullable: true })
  defaulter_date: Date;

  @Column({ type: 'enum', enum: DefaulterReason, nullable: true })
  defaulter_reason: DefaulterReason;
}
