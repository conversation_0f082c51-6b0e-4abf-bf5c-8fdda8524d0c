import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Enrollment } from '../entities/enrollment.entity';
import { CreateEnrollmentDto } from '../dto/create-enrollment.dto';
import { LeadProgramInterestService } from '@modules/leads/services/lead-program-interest.service';
import { LeadProgramInterest } from '@modules/leads/entities/lead-program-interest.entity';
import { EnrollmentStage } from '@modules/leads/enums/lead-level.enum';

@Injectable()
export class EnrollmentService {
  private readonly logger = new Logger(EnrollmentService.name);

  constructor(
    @InjectRepository(Enrollment)
    private enrollmentRepository: Repository<Enrollment>,
    @InjectRepository(LeadProgramInterest)
    private leadProgramInterestRepository: Repository<LeadProgramInterest>,
    private readonly leadProgramInterestService: LeadProgramInterestService,
  ) {}

  async findAll(): Promise<Enrollment[]> {
    try {
      this.logger.log('Fetching all enrollments');
      return await this.enrollmentRepository.find({
        relations: ['enrolled_by', 'lead_program_interest'],
      });
    } catch (error) {
      this.logger.error('Error fetching enrollments', error.stack);
      throw error;
    }
  }

  async handleEnrollment(
    createEnrollmentDto: CreateEnrollmentDto & {
      fresh_entity_data?: LeadProgramInterest;
    },
  ): Promise<{ isNew: boolean; enrollment: Enrollment }> {
    try {
      this.logger.log('Creating new enrollment', CreateEnrollmentDto);
      const {
        lead_program_interest_id,
        enrolled_by_id,
        enrollment_date,
        fresh_entity_data,
      } = createEnrollmentDto;

      if (!lead_program_interest_id || !enrolled_by_id || !enrollment_date) {
        throw new BadRequestException('Invalid enrollment data');
      }
      if (enrollment_date > new Date()) {
        throw new BadRequestException(
          'Enrollment date cannot be in the future',
        );
      }

      // Use fresh entity data if provided, otherwise fetch from database
      let leadProgramInterest: LeadProgramInterest;
      if (fresh_entity_data) {
        this.logger.log('Using fresh entity data from subscriber');
        leadProgramInterest = fresh_entity_data;
      } else {
        this.logger.log('Fetching LeadProgramInterest from database');
        // Add query hint to avoid cache and ensure fresh data
        leadProgramInterest = await this.leadProgramInterestService.findById(
          lead_program_interest_id,
        );

        // Double-check by refetching with relations if needed
        if (leadProgramInterest && !leadProgramInterest.lead_level) {
          leadProgramInterest =
            await this.leadProgramInterestRepository.findOne({
              where: { id: lead_program_interest_id },
              relations: ['lead_level', 'program', 'lead'],
              // Force fresh query from database
              cache: false,
            });
        }
      }

      if (!leadProgramInterest) {
        throw new BadRequestException('Lead Program Interest not found');
      }

      //find existing enrollment for the same lead program interest
      const existingEnrollment = await this.enrollmentRepository.findOne({
        where: {
          lead_program_interest_id: lead_program_interest_id?.toString(),
        },
      });

      if (existingEnrollment) {
        return {
          isNew: false,
          enrollment: existingEnrollment,
        };
      }

      if (
        leadProgramInterest?.lead_level?.enrollment_stage !==
        EnrollmentStage.Enrolled
      ) {
        throw new BadRequestException(
          'Lead Program Interest is not in the ENROLLED stage',
        );
      }

      //create a new enrollment
      const newEnrollment = this.enrollmentRepository.create({
        client_id: leadProgramInterest.lead.client_id,
        lead_program_interest_id: leadProgramInterest.id.toString(),
        enrolled_by_id: enrolled_by_id.toString(),
        enrollment_date: new Date(enrollment_date),
      });

      const savedEnrollment =
        await this.enrollmentRepository.save(newEnrollment);
      this.logger.log('Enrollment created successfully', savedEnrollment);

      return {
        isNew: true,
        enrollment: savedEnrollment,
      };
    } catch (error) {
      this.logger.error('Error creating enrollment', error.stack);
      throw new BadRequestException(
        'Failed to create enrollment. Please check the provided data.',
      );
    }
  }
}
