import { Controller, Get, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import { EnrollmentService } from './services/enrollment.service';
import { Enrollment } from './entities/enrollment.entity';

@ApiTags('enrollments')
@Controller('enrollments')
export class EnrollmentController {
  private readonly logger = new Logger(EnrollmentController.name);

  constructor(private readonly enrollmentService: EnrollmentService) {}

  @Get()
  @ApiOperation({ summary: 'Get all enrollments' })
  @ApiResponse({
    status: 200,
    description: 'List of all enrollments',
    type: [Enrollment],
  })
  async findAll(): Promise<Enrollment[]> {
    this.logger.log('GET /enrollments - Fetching all enrollments');
    return this.enrollmentService.findAll();
  }
}
